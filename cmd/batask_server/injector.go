//go:generate wire
//go:build wireinject
// +build wireinject

package main

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/services"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"github.com/google/wire"
)

func InitSaturnService() *services.SaturnService {
	saturnService, _ := initSaturnService()
	services.SetService(saturnService)

	return services.GetService()
}

func initSaturnService() (*services.SaturnService, error) {
	wire.Build(
		saturn_facade.ProviderSet,
		redisutil.Client,
	)
	return new(services.SaturnService), nil
}
