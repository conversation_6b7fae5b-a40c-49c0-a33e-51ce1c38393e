package common

import (
	"errors"
	"strconv"
	"strings"
)

func AtoiByPoint(sp *string) int {
	if sp == nil || *sp == "" {
		return 0
	}
	shopId, _ := strconv.Atoi(*sp)
	return shopId
}

func I64toU64ByPoint(ip *int64) uint64 {
	if ip == nil {
		return 0
	}
	return (uint64)(*ip)
}

func I64toI64ByPoint(ip *int64) int64 {
	if ip == nil {
		return 0
	}
	return *ip
}

func I64toIByPoint(ip *int64) int {
	if ip == nil {
		return 0
	}
	return int(*ip)
}

func U64toU64ByPoint(ip *uint64) uint64 {
	if ip == nil {
		return 0
	}
	return *ip
}

func I32toIByPoint(ip *int32) int {
	if ip == nil {
		return 0
	}
	return (int)(*ip)
}

func U32toIByPoint(ip *uint32) int {
	if ip == nil {
		return 0
	}
	return (int)(*ip)
}

func U32toU64ByPoint(ip *uint32) uint64 {
	if ip == nil {
		return 0
	}
	return (uint64)(*ip)
}

func F32toF64ByPoint(fp *float32) float64 {
	if fp == nil {
		return 0
	}
	return (float64)(*fp)
}

func U8toBoolByPoint(fb *uint8) bool {
	if fb == nil {
		return false
	}
	return *fb != 0
}

func StoI(s string) (int, error) {
	s = strings.TrimSpace(s)
	if s == "" {
		return 0, errors.New("content empty")
	}
	return strconv.Atoi(s)
}

func Int32toBoolByPoint(fb *int32) bool {
	if fb == nil {
		return false
	}
	return *fb != 0
}
