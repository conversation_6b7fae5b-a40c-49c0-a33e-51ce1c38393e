package objutil

import (
	"fmt"
	"regexp"
	"runtime"
	"strings"
)

const (
	_CurrentFileRelativePath = "/internal/util/pathutil/path.go"
)

var (
	projectAbsolutePath = genProjectAbsolutePath()
	papReg              = regexp.MustCompile(fmt.Sprintf("^%s", projectAbsolutePath))
)

// 获取当前函数位置，移去当前函数相对路径，即项目绝对路径
func genProjectAbsolutePath() string {
	_, filepath, _, _ := runtime.Caller(0)
	return strings.TrimRight(filepath, _CurrentFileRelativePath)
}

func GetProjectAbsolutePath() string {
	return projectAbsolutePath
}

func GetFileRelativePath(fileAbsolutePath string) string {
	if !papReg.MatchString(fileAbsolutePath) {
		return ""
	}
	return strings.Replace(fileAbsolutePath, projectAbsolutePath+"/", "", 1)
}
