# coding=utf-8
import pymysql
import json
import time


def init_db(env):
    if env == "test":
        cnx = pymysql.connect(
            host='',
            user='',
            password='',
            database='',
            port=6606
        )
        return cnx
    if env == "live":
        cnx = pymysql.connect(
            host='',
            user='',
            password='',
            database='',
            port=6606
        )
        return cnx


def delete_volume_sql(cnx, stat_time):
    cursor = cnx.cursor()
    try:
        # 查询最大id
        print "begin"
        start = time.time()
        max_id_sql = "select max(id) from routing_product_order_num_tab where stat_time <= %s"
        max_id_param = (stat_time,)
        cursor.execute(max_id_sql, max_id_param)
        result = cursor.fetchall()
        max_id = 0
        for row in result:
            max_id = row[0]
        end = time.time()
        print "select cost time", end, start, end - start, max_id
        # 删除小于max id的数据
        row_affected = 1
        delete_num = 0
        while row_affected != 0 and max_id is not None and max_id != 0:
            start = time.time()
            delete_sql = "delete from routing_product_order_num_tab where id <= %s limit 1000"
            delete_param = (max_id,)
            cursor.execute(delete_sql, delete_param)
            row_affected = cursor.rowcount
            cnx.commit()
            delete_num += 1
            end = time.time()
            print "delete cost time", delete_num, end, start, end - start
            if delete_num > 1000:
                break
        print "end"
    except Exception as e:
        print e
    print "end"


def test_sql():
    mysql_connection = init_db("test")
    delete_volume_sql(mysql_connection, "********")


def live_sql():
    mysql_connection = init_db("live")
    delete_volume_sql(mysql_connection, "********")


if __name__ == "__main__":
    live_sql()
