package ilh_forecast_task

import (
	"bytes"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	availableLHEntity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	availableLHRepo "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane/lane_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/repo"
	ruleEntity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/ilh_smart_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	// BlockedLineID 表示被Block订单的LineID
	BlockedLineID = "BLOCKED"

	// BlockedResultKeyPrefix 表示被Block订单结果的Key前缀
	BlockedResultKeyPrefix = "BLOCK_"

	// BlockedDestPort 表示被Block订单的目的港标识
	BlockedDestPort = "BLOCKED_DEST_PORT"
)

// isBlockedRecord 检查记录是否为被Block订单
func isBlockedRecord(laneCode string) bool {
	return laneCode == forecast.Blocked || laneCode == forecast.RuleBlocked || laneCode == forecast.RoutingFailed
}

// generateBlockedResultKey 生成被Block订单结果的Key
func generateBlockedResultKey(multiProductID int) string {
	return fmt.Sprintf("%s%d", BlockedResultKeyPrefix, multiProductID)
}

// isBlockedResultKey 检查结果Key是否为Block记录
func isBlockedResultKey(key string) bool {
	return strings.HasPrefix(key, BlockedResultKeyPrefix)
}

// generateLhResultKey 生成LH结果的Map Key
func generateLhResultKey(lineID string, destPort string, dgType ruleEntity.DGFlag) string {
	return fmt.Sprintf("%s_%s_%d", lineID, destPort, dgType)
}

// generateRuleInfoKey 生成规则信息的Map Key
func generateRuleInfoKey(ruleName string, multiProductID int) string {
	return fmt.Sprintf("%s_%d", ruleName, multiProductID)
}

// generateRuleMinWeightMapKey 生成Rule MinWeight Map的Key
func generateRuleMinWeightMapKey(multiProductID int, ruleName string, lineID string) string {
	return fmt.Sprintf("%d_%s_%s", multiProductID, ruleName, lineID)
}

// ILHForecastTaskService ILH预测任务服务接口
type ILHForecastTaskService interface {
	// Create 创建ILH预测任务
	Create(ctx context.Context, req *ilh_smart_routing.CreateILHForecastTaskReq) (int, *srerr.Error)

	// Update 更新ILH预测任务
	Update(ctx context.Context, req *ilh_smart_routing.UpdateILHForecastTaskReq) *srerr.Error

	// Get 获取ILH预测任务详情
	Get(ctx context.Context, id int) (*ilh_smart_routing.GetILHForecastTaskResp, *srerr.Error)

	// List 获取ILH预测任务列表
	List(ctx context.Context, req *ilh_smart_routing.ListILHForecastTaskReq) (*ilh_smart_routing.ListILHForecastTaskResp, *srerr.Error)

	// Delete 删除ILH预测任务
	Delete(ctx context.Context, id int) *srerr.Error

	// Copy 复制ILH预测任务
	Copy(ctx context.Context, req *ilh_smart_routing.CopyILHForecastTaskReq) (int, *srerr.Error)

	// GetResult 获取ILH预测任务结果
	GetResult(ctx context.Context, req *ilh_smart_routing.GetILHForecastTaskResultReq) (*ilh_smart_routing.GetILHForecastTaskResultResp, *srerr.Error)

	// Deploy 部署ILH预测任务
	Deploy(ctx context.Context, req *ilh_smart_routing.DeployILHForecastTaskReq) (*ilh_smart_routing.DeployILHForecastTaskResp, *srerr.Error)

	// UpdateTaskStatus 更新ILH预测任务状态
	UpdateTaskStatus(ctx context.Context, taskID int, taskStatus persistent.TaskStatus) *srerr.Error

	// UpdateDeployStatus 更新ILH预测任务部署状态
	UpdateDeployStatus(ctx context.Context, taskID int, deployStatus persistent.DeployStatus) *srerr.Error

	// Export 导出ILH预测任务结果
	Export(ctx context.Context, req *ilh_smart_routing.ExportILHForecastTaskResultReq) (*ilh_smart_routing.ExportILHForecastTaskResultResp, *srerr.Error)

	// BatchSaveResults 批量保存ILH预测任务结果
	BatchSaveResults(ctx context.Context, results []*forecastentity.ILHForecastResult) *srerr.Error
}

// ILHForecastTaskServiceImpl ILH预测任务服务实现
type ILHForecastTaskServiceImpl struct {
	ilhForecastTaskRepo forecastrepo.ILHForecastTaskRepo
	availableLHRepo     availableLHRepo.AvailableLHRepo
	lhCapacityRepo      repo.LHCapacityRepo
	laneService         lane.LaneService
	lpsApi              lpsclient.LpsApi
}

// NewILHForecastTaskServiceImpl 创建ILH预测任务服务实现
func NewILHForecastTaskServiceImpl(
	ilhForecastTaskRepo forecastrepo.ILHForecastTaskRepo,
	availableLHRepo availableLHRepo.AvailableLHRepo,
	lhCapacityRepo repo.LHCapacityRepo,
	laneService lane.LaneService,
	lpsApi lpsclient.LpsApi,
) *ILHForecastTaskServiceImpl {
	return &ILHForecastTaskServiceImpl{
		ilhForecastTaskRepo: ilhForecastTaskRepo,
		availableLHRepo:     availableLHRepo,
		lhCapacityRepo:      lhCapacityRepo,
		laneService:         laneService,
		lpsApi:              lpsApi,
	}
}

// Create 创建ILH预测任务
func (s *ILHForecastTaskServiceImpl) Create(ctx context.Context, req *ilh_smart_routing.CreateILHForecastTaskReq) (int, *srerr.Error) {
	// 转换请求为实体
	task := &persistent.ILHForecastTaskTab{
		TaskName:         req.TaskName,
		StartDate:        req.StartDate,
		EndDate:          req.EndDate,
		ShipmentResource: req.ShipmentResource,
		TaskOperator:     req.Operator,
		TaskStatus:       req.TaskStatus,
		DeployStatus:     persistent.DeployStatusInit,
	}

	// 调用仓储层创建任务
	id, err := s.ilhForecastTaskRepo.CreateILHForecastTask(ctx, task)
	if err != nil {
		return 0, err
	}

	// 批量保存可用LH规则
	if len(req.AvailableLHRuleList) > 0 {
		forecastRules := make([]*availableLHRepo.ForecastAvailableLHTab, 0, len(req.AvailableLHRuleList))
		for _, forecastRule := range req.AvailableLHRuleList {
			forecastRules = append(forecastRules, &availableLHRepo.ForecastAvailableLHTab{
				TaskID:         id,
				MultiProductID: forecastRule.MultiProductID,
				Rules:          availableLHRepo.Rules(forecastRule.Rules),
			})
		}

		// 调用批量保存方法
		if err := s.availableLHRepo.BatchSaveForecastAvailableLHRules(ctx, id, forecastRules); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to batch save forecast available route rules: taskId=%d, err=%v", id, err)
			return id, err
		}
	}

	// 批量保存LH容量配置
	if len(req.LHCapacityList) > 0 {
		forecastCapacities := make([]*repo.ForecastLHCapacityTab, 0, len(req.LHCapacityList))
		for _, capacityItem := range req.LHCapacityList {
			forecastCapacities = append(forecastCapacities, &repo.ForecastLHCapacityTab{
				TaskID:           strconv.Itoa(id),
				CapacityName:     capacityItem.CapacityName,
				ILHVendorName:    capacityItem.ILHVendorName,
				ILHLineID:        capacityItem.ILHLineID,
				ILHLineName:      capacityItem.ILHLineName,
				TWS:              repo.TWSList(capacityItem.TWS),
				DestinationPort:  repo.DestinationList(capacityItem.DestinationPort),
				DGType:           capacityItem.DGType,
				CapacitySettings: repo.CapacitySettingsList(capacityItem.CapacitySettings),
			})
		}

		// 调用批量保存方法
		if err := s.lhCapacityRepo.BatchSaveForecastLHCapacities(ctx, id, forecastCapacities); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to batch save forecast LH capacities: taskId=%d, err=%v", id, err)
			return id, err
		}
	}

	return id, nil
}

// Update 更新ILH预测任务
func (s *ILHForecastTaskServiceImpl) Update(ctx context.Context, req *ilh_smart_routing.UpdateILHForecastTaskReq) *srerr.Error {
	// 检查任务是否存在
	existTask, err := s.ilhForecastTaskRepo.GetILHForecastTaskByID(ctx, req.ID)
	if err != nil {
		return err
	}

	// 更新任务信息
	existTask.TaskName = req.TaskName
	existTask.StartDate = req.StartDate
	existTask.EndDate = req.EndDate
	existTask.TaskStatus = req.TaskStatus
	existTask.ShipmentResource = req.ShipmentResource
	existTask.TaskOperator = req.Operator

	// 调用仓储层更新任务
	if err := s.ilhForecastTaskRepo.UpdateILHForecastTask(ctx, existTask); err != nil {
		return err
	}

	// 批量更新可用LH规则
	if len(req.AvailableLHRuleList) > 0 {
		forecastRules := make([]*availableLHRepo.ForecastAvailableLHTab, 0, len(req.AvailableLHRuleList))
		for _, forecastRule := range req.AvailableLHRuleList {
			forecastRules = append(forecastRules, &availableLHRepo.ForecastAvailableLHTab{
				TaskID:         req.ID,
				MultiProductID: forecastRule.MultiProductID,
				Rules:          availableLHRepo.Rules(forecastRule.Rules),
			})
		}

		// 调用批量保存方法（先删除再插入）
		if err := s.availableLHRepo.BatchSaveForecastAvailableLHRules(ctx, req.ID, forecastRules); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to batch update forecast available route rules: taskId=%d, err=%v", req.ID, err)
			return err
		}
	}

	// 批量更新LH容量配置
	if len(req.LHCapacityList) > 0 {
		forecastCapacities := make([]*repo.ForecastLHCapacityTab, 0, len(req.LHCapacityList))
		for _, capacityItem := range req.LHCapacityList {
			forecastCapacities = append(forecastCapacities, &repo.ForecastLHCapacityTab{
				TaskID:           strconv.Itoa(req.ID),
				CapacityName:     capacityItem.CapacityName,
				ILHVendorName:    capacityItem.ILHVendorName,
				ILHLineID:        capacityItem.ILHLineID,
				ILHLineName:      capacityItem.ILHLineName,
				TWS:              repo.TWSList(capacityItem.TWS),
				DestinationPort:  repo.DestinationList(capacityItem.DestinationPort),
				DGType:           capacityItem.DGType,
				CapacitySettings: repo.CapacitySettingsList(capacityItem.CapacitySettings),
			})
		}

		// 调用批量保存方法（先删除再插入）
		if err := s.lhCapacityRepo.BatchSaveForecastLHCapacities(ctx, req.ID, forecastCapacities); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to batch update forecast LH capacities: taskId=%d, err=%v", req.ID, err)
			return err
		}
	}

	return nil
}

// Get 获取ILH预测任务详情
func (s *ILHForecastTaskServiceImpl) Get(ctx context.Context, id int) (*ilh_smart_routing.GetILHForecastTaskResp, *srerr.Error) {
	// 调用仓储层获取任务
	task, err := s.ilhForecastTaskRepo.GetILHForecastTaskByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取可用LH规则
	var availableLHRuleList []forecastentity.ForecastAvailableLHRule
	forecastRulesList, err := s.availableLHRepo.GetForecastAvailableLHRulesByTaskID(ctx, id)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get forecast available route rules: taskId=%d, err=%v", id, err)
		// 不影响主流程，继续处理
	} else if forecastRulesList != nil && len(forecastRulesList) > 0 {
		// 转换为新的ForecastAvailableLHRule类型列表
		for _, forecastRule := range forecastRulesList {
			if forecastRule != nil && len(forecastRule.Rules) > 0 {
				availableLHRuleList = append(availableLHRuleList, forecastentity.ForecastAvailableLHRule{
					MultiProductID: forecastRule.MultiProductID,
					Rules:          []availableLHEntity.AvailableLHRule(forecastRule.Rules),
				})
			}
		}
	}

	// 获取LH容量配置
	var capacityItems []forecastentity.ForecastLHCapacityItem
	forecastCapacities, err := s.lhCapacityRepo.GetForecastLHCapacitiesByTaskID(ctx, id)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get forecast LH capacities: taskId=%d, err=%v", id, err)
		// 不影响主流程，继续处理
	} else if len(forecastCapacities) > 0 {
		for _, capacity := range forecastCapacities {
			item := forecastentity.ForecastLHCapacityItem{
				CapacityName:     capacity.CapacityName,
				ILHVendorName:    capacity.ILHVendorName,
				ILHLineID:        capacity.ILHLineID,
				ILHLineName:      capacity.ILHLineName,
				TWS:              []string(capacity.TWS),
				DestinationPort:  []string(capacity.DestinationPort),
				DGType:           capacity.DGType,
				CapacitySettings: capacity.CapacitySettings,
			}
			capacityItems = append(capacityItems, item)
		}
	}

	// 转换为响应
	resp := &ilh_smart_routing.GetILHForecastTaskResp{
		ID:                   task.ID,
		TaskName:             task.TaskName,
		StartDate:            task.StartDate,
		EndDate:              task.EndDate,
		TaskStatus:           task.TaskStatus,
		DeployStatus:         task.DeployStatus,
		ShipmentResource:     task.ShipmentResource,
		AvailableLHRuleList:  availableLHRuleList,
		LHCapacityList:       capacityItems,
		ForecastCompleteTime: int(task.CompleteTime),
		TaskOperator:         task.TaskOperator,
		DeployTime:           int(task.DeployTime),
		DeployOperator:       task.DeployOperator,
	}

	return resp, nil
}

// List 获取ILH预测任务列表
func (s *ILHForecastTaskServiceImpl) List(ctx context.Context, req *ilh_smart_routing.ListILHForecastTaskReq) (*ilh_smart_routing.ListILHForecastTaskResp, *srerr.Error) {
	// 调用仓储层获取任务列表
	tasks, total, err := s.ilhForecastTaskRepo.ListILHForecastTasks(ctx, req)
	if err != nil {
		return nil, err
	}

	// 转换为响应
	items := make([]ilh_smart_routing.ListILHForecastTaskRespItem, 0, len(tasks))
	for _, task := range tasks {
		item := ilh_smart_routing.ListILHForecastTaskRespItem{
			ID:                   task.ID,
			TaskName:             task.TaskName,
			TaskStatus:           task.TaskStatus,
			DeployStatus:         task.DeployStatus,
			ForecastCompleteTime: int(task.CompleteTime),
			TaskOperator:         task.TaskOperator,
			DeployTime:           int(task.DeployTime),
			DeployOperator:       task.DeployOperator,
		}

		// 获取任务关联的可用LH规则，填充MultiProductIDList
		forecastRulesList, rErr := s.availableLHRepo.GetForecastAvailableLHRulesByTaskID(ctx, task.ID)
		if rErr != nil {
			logger.CtxLogErrorf(ctx, "Failed to get forecast available route rules for task list: taskId=%d, err=%v", task.ID, rErr)
			// 不影响主流程，继续处理
		} else if forecastRulesList != nil && len(forecastRulesList) > 0 {
			// 使用map去重
			multiProductIDMap := make(map[int]struct{})
			for _, forecastRule := range forecastRulesList {
				if forecastRule != nil {
					multiProductIDMap[forecastRule.MultiProductID] = struct{}{}
				}
			}

			// 转换为列表
			multiProductIDList := make([]int, 0, len(multiProductIDMap))
			for mpID := range multiProductIDMap {
				multiProductIDList = append(multiProductIDList, mpID)
			}
			item.MultiProductIDList = multiProductIDList
		}

		// 获取任务关联的LH容量配置，填充ILHLineList
		forecastCapacities, cErr := s.lhCapacityRepo.GetForecastLHCapacitiesByTaskID(ctx, task.ID)
		if cErr != nil {
			logger.CtxLogErrorf(ctx, "Failed to get forecast LH capacities for task list: taskId=%d, err=%v", task.ID, cErr)
			// 不影响主流程，继续处理
		} else if len(forecastCapacities) > 0 {
			// 使用map去重
			ilhLineMap := make(map[string]string) // key: lineID, value: lineName
			for _, capacity := range forecastCapacities {
				ilhLineMap[capacity.ILHLineID] = capacity.ILHLineName
			}

			// 转换为列表
			ilhLineList := make([]ilh_smart_routing.ListILHLineInfo, 0, len(ilhLineMap))
			for lineID, lineName := range ilhLineMap {
				ilhLineList = append(ilhLineList, ilh_smart_routing.ListILHLineInfo{
					ILHLineID:   lineID,
					ILHLineName: lineName,
				})
			}
			item.ILHLineList = ilhLineList
		}

		items = append(items, item)
	}

	resp := &ilh_smart_routing.ListILHForecastTaskResp{
		Total:  total,
		Pageno: req.Pageno,
		Count:  len(items),
		List:   items,
	}

	return resp, nil
}

// Delete 删除ILH预测任务
func (s *ILHForecastTaskServiceImpl) Delete(ctx context.Context, id int) *srerr.Error {
	// 先删除相关的可用路线规则
	if err := s.availableLHRepo.DeleteForecastAvailableLHRulesByTaskID(ctx, id); err != nil {
		logger.CtxLogErrorf(ctx, "Failed to delete forecast available route rules: taskId=%d, err=%v", id, err)
		// 不影响主流程，继续删除其他相关数据
	}

	// 删除相关的LH容量配置
	if err := s.lhCapacityRepo.DeleteForecastLHCapacitiesByTaskID(ctx, id); err != nil {
		logger.CtxLogErrorf(ctx, "Failed to delete forecast LH capacities: taskId=%d, err=%v", id, err)
		// 不影响主流程，继续删除任务
	}

	// 调用仓储层删除任务
	return s.ilhForecastTaskRepo.DeleteILHForecastTask(ctx, id)
}

// Copy 复制ILH预测任务
func (s *ILHForecastTaskServiceImpl) Copy(ctx context.Context, req *ilh_smart_routing.CopyILHForecastTaskReq) (int, *srerr.Error) {
	// 检查源任务是否存在
	_, err := s.ilhForecastTaskRepo.GetILHForecastTaskByID(ctx, req.ID)
	if err != nil {
		return 0, err
	}

	// 复制任务
	newId, err := s.ilhForecastTaskRepo.CopyILHForecastTask(ctx, req.ID)
	if err != nil {
		return 0, err
	}

	// 批量复制可用路线规则
	forecastRulesList, err := s.availableLHRepo.GetForecastAvailableLHRulesByTaskID(ctx, req.ID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get available rules when copying forecast task: srcTaskId=%d, err=%v", req.ID, err)
		// 不影响主流程，返回新任务ID
		return newId, nil
	}

	if len(forecastRulesList) > 0 {
		// 构建新的规则列表
		newForecastRules := make([]*availableLHRepo.ForecastAvailableLHTab, 0, len(forecastRulesList))
		for _, forecastRules := range forecastRulesList {
			if forecastRules != nil && len(forecastRules.Rules) > 0 {
				newForecastRules = append(newForecastRules, &availableLHRepo.ForecastAvailableLHTab{
					TaskID:         newId,
					MultiProductID: forecastRules.MultiProductID,
					Rules:          forecastRules.Rules,
				})
			}
		}

		// 批量保存规则
		if err := s.availableLHRepo.BatchSaveForecastAvailableLHRules(ctx, newId, newForecastRules); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to batch save available when copying forecast task: newTaskId=%d, err=%v", newId, err)
			// 不影响主流程，继续复制LH容量配置
		}
	}

	// 批量复制LH容量配置
	forecastCapacities, err := s.lhCapacityRepo.GetForecastLHCapacitiesByTaskID(ctx, req.ID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get LH capacities when copying forecast task: srcTaskId=%d, err=%v", req.ID, err)
		// 不影响主流程，返回新任务ID
		return newId, nil
	}

	if len(forecastCapacities) > 0 {
		// 构建新的容量配置列表
		newCapacities := make([]*repo.ForecastLHCapacityTab, 0, len(forecastCapacities))
		for _, capacity := range forecastCapacities {
			newCapacities = append(newCapacities, &repo.ForecastLHCapacityTab{
				TaskID:           strconv.Itoa(newId),
				CapacityName:     capacity.CapacityName,
				ILHVendorName:    capacity.ILHVendorName,
				ILHLineID:        capacity.ILHLineID,
				ILHLineName:      capacity.ILHLineName,
				TWS:              capacity.TWS,
				DestinationPort:  capacity.DestinationPort,
				DGType:           capacity.DGType,
				CapacitySettings: capacity.CapacitySettings,
			})
		}

		// 批量保存配置
		if err := s.lhCapacityRepo.BatchSaveForecastLHCapacities(ctx, newId, newCapacities); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to batch save LH capacities when copying forecast task: newTaskId=%d, err=%v", newId, err)
			// 不影响主流程，继续处理
		}
	}

	return newId, nil
}

// GetResult 获取ILH预测任务结果
func (s *ILHForecastTaskServiceImpl) GetResult(ctx context.Context, req *ilh_smart_routing.GetILHForecastTaskResultReq) (*ilh_smart_routing.GetILHForecastTaskResultResp, *srerr.Error) {
	// 从仓储层获取原始任务结果数据
	results, err := s.ilhForecastTaskRepo.GetILHForecastTaskResultData(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 获取产品名称映射
	productNameMap, prodErr := s.getProductNameMap(ctx)
	if prodErr != nil {
		// 仅记录错误，不中断流程
		logger.CtxLogErrorf(ctx, "Failed to get product name map: %v", prodErr)
	}

	// 获取预测任务关联的可用LH规则
	forecastRulesList, ruleErr := s.availableLHRepo.GetForecastAvailableLHRulesByTaskID(ctx, req.ID)
	if ruleErr != nil {
		// 获取规则失败，记录错误，但继续处理，MinRequiredWeight 将为 0
		logger.CtxLogErrorf(ctx, "Failed to get forecast available LH rules for task result: taskId=%d, err=%v", req.ID, ruleErr)
		forecastRulesList = make([]*availableLHRepo.ForecastAvailableLHTab, 0) // 使用空切片继续
	}

	// 如果没有结果数据，直接返回空结果
	if len(results) == 0 {
		return &ilh_smart_routing.GetILHForecastTaskResultResp{
			LHResults:      make([]ilh_smart_routing.ForecastLHResult, 0),
			CCResults:      make([]ilh_smart_routing.ForecastCCResult, 0),
			ProductResults: make([]ilh_smart_routing.ForecastProductResult, 0),
		}, nil
	}

	// 处理结果数据，并构建响应 (传入原始结果、规则列表和产品名称映射)
	return s.buildResultResponse(ctx, results, forecastRulesList, productNameMap)
}

// getProductNameMap 获取产品名称映射
// 获取所有产品ID与名称的映射关系，如果获取失败则返回空映射
func (s *ILHForecastTaskServiceImpl) getProductNameMap(ctx context.Context) (map[int64]string, error) {
	productNameMap, err := s.lpsApi.GetAllProductIdNameList(ctx)
	if err != nil {
		// 报错，但不Block流程
		logger.CtxLogErrorf(ctx, "Failed to get all product id name list: err=%v", err)
		return make(map[int64]string), nil
	}
	return productNameMap, nil
}

// forecastResultData 预测结果处理过程中的临时数据结构
// 用于存储处理过程中的中间结果，减少函数参数传递
type forecastResultData struct {
	lhResultMap      map[string]ilh_smart_routing.ForecastLHResult        // key: 由 generateLhResultKey(lineID, destPort, dgType) 生成
	productResultMap map[int]ilh_smart_routing.ForecastProductResult      // key: multiProductID
	ruleInfoMap      map[string]ilh_smart_routing.ForecastProductRuleInfo // key: 常规情况由 generateRuleInfoKey(ruleName, multiProductID) 生成; Blocked记录有特定格式，详见 processBlockedProductResult
	importILHInfoMap map[string]ilh_smart_routing.ILHLineInfo             // key: LineID (Import ILH Line ID)
	destPortDGMap    map[string]map[string]map[ruleEntity.DGFlag]bool     // key: importILHLineID, value: map[destPort]map[dgType]bool
}

// buildResultResponse 构建结果响应
// 处理原始预测结果数据，转换为前端需要的结构化响应
func (s *ILHForecastTaskServiceImpl) buildResultResponse(
	ctx context.Context,
	originalResults []persistent.ILHForecastTaskResultTab,
	forecastRulesList []*availableLHRepo.ForecastAvailableLHTab,
	productNameMap map[int64]string,
) (*ilh_smart_routing.GetILHForecastTaskResultResp, *srerr.Error) {
	// 创建响应结构
	resp := &ilh_smart_routing.GetILHForecastTaskResultResp{
		LHResults:      make([]ilh_smart_routing.ForecastLHResult, 0),
		CCResults:      make([]ilh_smart_routing.ForecastCCResult, 0),
		ProductResults: make([]ilh_smart_routing.ForecastProductResult, 0),
	}

	// 临时存储处理结果的数据结构
	resultData := &forecastResultData{
		lhResultMap:      make(map[string]ilh_smart_routing.ForecastLHResult),        // key: 由 generateLhResultKey(lineID, destPort, dgType) 生成
		productResultMap: make(map[int]ilh_smart_routing.ForecastProductResult),      // key: multiProductID
		ruleInfoMap:      make(map[string]ilh_smart_routing.ForecastProductRuleInfo), // key: 常规情况由 generateRuleInfoKey(ruleName, multiProductID) 生成; Blocked记录有特定格式，详见 processBlockedProductResult
		importILHInfoMap: make(map[string]ilh_smart_routing.ILHLineInfo),             // key: LineID (Import ILH Line ID)
		destPortDGMap:    make(map[string]map[string]map[ruleEntity.DGFlag]bool),     // key: importILHLineID, value: map[destPort]map[dgType]bool
	}

	// 创建 Rule MinRequiredWeight 查找 Map
	ruleMinWeightMap := s.buildRuleMinWeightMap(ctx, forecastRulesList)

	// 处理查询结果
	s.processOriginalResults(ctx, originalResults, productNameMap, resultData)

	// 计算各ILH分配重量的比例
	s.calculateWeightProportions(resultData)

	// 转换Map为切片
	resp.LHResults = s.populateLHResults(resultData)

	resp.ProductResults = s.populateProductResults(resultData)

	// 处理CC结果 - 支持多个CC Result
	resp.CCResults = s.populateCCResults(ctx, resultData, originalResults, ruleMinWeightMap, productNameMap)

	return resp, nil
}

// populateLHResults 从resultData中填充LHResults
func (s *ILHForecastTaskServiceImpl) populateLHResults(resultData *forecastResultData) []ilh_smart_routing.ForecastLHResult {
	lhResults := make([]ilh_smart_routing.ForecastLHResult, 0, len(resultData.lhResultMap))
	// 首先处理正常结果
	for key, lhResult := range resultData.lhResultMap {
		// Block结果需要在最后显示，暂时跳过
		if isBlockedResultKey(key) {
			continue
		}
		lhResults = append(lhResults, lhResult)
	}

	// 然后添加所有Block结果
	for key, lhResult := range resultData.lhResultMap {
		if isBlockedResultKey(key) {
			lhResults = append(lhResults, lhResult)
		}
	}
	return lhResults
}

// populateProductResults 从resultData中填充ProductResults
func (s *ILHForecastTaskServiceImpl) populateProductResults(resultData *forecastResultData) []ilh_smart_routing.ForecastProductResult {
	productResults := make([]ilh_smart_routing.ForecastProductResult, 0, len(resultData.productResultMap))
	for _, productResult := range resultData.productResultMap {
		productResults = append(productResults, productResult)
	}
	return productResults
}

// populateCCResults 从resultData和其他输入中填充CCResults
func (s *ILHForecastTaskServiceImpl) populateCCResults(ctx context.Context, resultData *forecastResultData, originalResults []persistent.ILHForecastTaskResultTab, ruleMinWeightMap map[string]float64, productNameMap map[int64]string) []ilh_smart_routing.ForecastCCResult {
	ccResults := make([]ilh_smart_routing.ForecastCCResult, 0)
	for importLineID, importILHInfo := range resultData.importILHInfoMap {
		if destPortDGTypes, exists := resultData.destPortDGMap[importLineID]; exists {
			for destPort, dgTypes := range destPortDGTypes {
				for dgType := range dgTypes {
					ccResult := s.buildCCResult(ctx, resultData, originalResults, ruleMinWeightMap, productNameMap, importILHInfo, destPort, dgType)
					ccResults = append(ccResults, ccResult)
				}
			}
		}
	}
	return ccResults
}

// buildRuleMinWeightMap 创建 Rule MinRequiredWeight 查找 Map
// key: "MultiProductID_RuleName_LineID", value: MinWeight
func (s *ILHForecastTaskServiceImpl) buildRuleMinWeightMap(ctx context.Context, forecastRulesList []*availableLHRepo.ForecastAvailableLHTab) map[string]float64 {
	ruleMinWeightMap := make(map[string]float64)

	for _, fRule := range forecastRulesList {
		if fRule == nil {
			continue
		}
		multiProductID := fRule.MultiProductID

		for _, rule := range fRule.Rules {
			ruleName := rule.RuleName
			if ruleName == "" {
				logger.CtxLogErrorf(ctx, "Rule name is empty in ForecastAvailableLHTab for MultiProductID %d. Skipping rule.", multiProductID)
				continue // 需要规则标识符
			}

			// 修复 AvailableLine 字段访问，根据实际的结构体定义调整
			for _, availableLine := range rule.AvailableLine {
				// 遍历LineList获取实际线路信息
				for _, lineInfo := range availableLine.LineList {
					// 只使用基础组合键: "MultiProductID_RuleName_LineID"
					baseKey := generateRuleMinWeightMapKey(multiProductID, ruleName, lineInfo.LineId)
					ruleMinWeightMap[baseKey] = lineInfo.MinWeight
				}
			}
		}
	}
	return ruleMinWeightMap
}

// processOriginalResults 处理原始查询结果并填充resultData
func (s *ILHForecastTaskServiceImpl) processOriginalResults(ctx context.Context, originalResults []persistent.ILHForecastTaskResultTab, productNameMap map[int64]string, resultData *forecastResultData) {
	for _, result := range originalResults {
		if err := s.processResultItem(ctx, &result, productNameMap, resultData); err != nil {
			logger.CtxLogErrorf(ctx, "Skip processing result item: laneCode=%s, err=%v", result.LaneCode, err)
			continue
		}
	}
}

// processResultItem 处理单个结果项
// 从原始结果数据中提取信息，填充到临时数据结构中
func (s *ILHForecastTaskServiceImpl) processResultItem(
	ctx context.Context,
	result *persistent.ILHForecastTaskResultTab,
	productNameMap map[int64]string,
	resultData *forecastResultData,
) error {
	// 特殊处理Block记录
	if isBlockedRecord(result.LaneCode) {
		// 分别处理Block记录的三种结果
		s.processBlockedILHResult(result, productNameMap, resultData)          // 直接处理LH结果
		s.processBlockedProductResult(ctx, result, productNameMap, resultData) // 处理产品结果
		s.processBlockedImportILH(result, resultData)                          // 处理ImportILH结果

		return nil // Block记录处理完成
	}

	// 以下为正常记录的处理逻辑
	// 获取Lane信息
	laneInfo, laneErr := s.laneService.GetLaneInfoByLaneCode(ctx, result.LaneCode)
	if laneErr != nil {
		logger.CtxLogErrorf(ctx, "Failed to get lane info: laneCode=%s, err=%v", result.LaneCode, laneErr)
		return laneErr
	}

	// 获取ILH线路信息
	ilhLineInfo := laneInfo.GetCILHLineInfo()
	// 获取Import ILH(CC)线路信息
	importILHLineInfo := laneInfo.GetImportILHLineInfo()

	// 如果线路信息不存在，跳过处理
	if ilhLineInfo == nil || ilhLineInfo.LineID == "" {
		return fmt.Errorf("ilh line info not found")
	}

	// 生成ILHLineInfo结构
	ilhLine := ilh_smart_routing.ILHLineInfo{
		LineID:   ilhLineInfo.LineID,
		LineName: ilhLineInfo.LineName,
	}

	// 处理进口ILH线路信息（CC）
	if importILHLineInfo != nil && importILHLineInfo.LineID != "" {
		importLineID := importILHLineInfo.LineID
		resultData.importILHInfoMap[importLineID] = ilh_smart_routing.ILHLineInfo{
			LineID:   importILHLineInfo.LineID,
			LineName: importILHLineInfo.LineName,
		}

		// 添加目的港和DG类型映射
		if _, exists := resultData.destPortDGMap[importLineID]; !exists {
			resultData.destPortDGMap[importLineID] = make(map[string]map[ruleEntity.DGFlag]bool)
		}

		if _, exists := resultData.destPortDGMap[importLineID][result.DestinationPort]; !exists {
			resultData.destPortDGMap[importLineID][result.DestinationPort] = make(map[ruleEntity.DGFlag]bool)
		}

		resultData.destPortDGMap[importLineID][result.DestinationPort][result.DgType] = true
	}

	// 处理LH结果
	s.processLHResult(result, ilhLine, productNameMap, resultData)

	// 处理产品结果
	var importILH ilh_smart_routing.ILHLineInfo
	if importILHLineInfo != nil && importILHLineInfo.LineID != "" {
		importILH = resultData.importILHInfoMap[importILHLineInfo.LineID]
	}
	s.processProductResult(ctx, result, ilhLine, importILH, productNameMap, resultData)

	return nil
}

// processLHResult 处理LH结果
// 处理单条记录的LH相关信息，填充到临时数据结构中
func (s *ILHForecastTaskServiceImpl) processLHResult(
	result *persistent.ILHForecastTaskResultTab,
	ilhLine ilh_smart_routing.ILHLineInfo,
	productNameMap map[int64]string,
	resultData *forecastResultData,
) {
	// 正常记录的处理逻辑
	lhResultKey := generateLhResultKey(ilhLine.LineID, result.DestinationPort, result.DgType)
	lhResult, exists := resultData.lhResultMap[lhResultKey]
	if !exists {
		lhResult = ilh_smart_routing.ForecastLHResult{
			ILHLine:         ilhLine,
			DestinationPort: result.DestinationPort,
			DGType:          result.DgType,
			ProductInfos:    make([]ilh_smart_routing.ForecastLHProductInfo, 0),
		}
	}

	// 计算总重量，用于后续比例计算
	totalWeight := result.ReversedBSAWeight + result.NonReversedBSAWeight + result.AdhocWeight

	// 获取对应的产品信息
	productInfo := ilh_smart_routing.ForecastLHProductInfo{
		ProductID:                result.MultiProductID,
		ProductName:              productNameMap[int64(result.MultiProductID)],
		ReservedBSA:              result.ReversedBSAWeight,
		ReservedBSAProportion:    calculateProportion(result.ReversedBSAWeight, totalWeight),
		NonReservedBSA:           result.NonReversedBSAWeight,
		NonReservedBSAProportion: calculateProportion(result.NonReversedBSAWeight, totalWeight),
		AdhocWeight:              result.AdhocWeight,
		AdhocWeightProportion:    calculateProportion(result.AdhocWeight, totalWeight),
	}

	// 添加产品信息到LH结果
	lhResult.ProductInfos = append(lhResult.ProductInfos, productInfo)

	// 将更新后的结果存回map
	resultData.lhResultMap[lhResultKey] = lhResult
}

// processBlockedILHResult 处理被Block订单的LH结果
func (s *ILHForecastTaskServiceImpl) processBlockedILHResult(
	result *persistent.ILHForecastTaskResultTab,
	productNameMap map[int64]string,
	resultData *forecastResultData,
) {
	// 对于Block记录，使用特殊键值
	blockResultKey := generateBlockedResultKey(result.MultiProductID)
	blockResult, exists := resultData.lhResultMap[blockResultKey]
	if !exists {
		blockResult = ilh_smart_routing.ForecastLHResult{
			ILHLine: ilh_smart_routing.ILHLineInfo{
				LineID:   BlockedLineID,
				LineName: BlockedLineID,
			},
			DGType:       result.DgType,
			ProductInfos: make([]ilh_smart_routing.ForecastLHProductInfo, 0),
		}
	}

	// 对于Block记录，我们只关心总重量
	totalWeight := result.AdhocWeight // 对于Block记录，所有重量都存储在AdhocWeight字段中

	// 查找现有产品信息或创建新的
	var foundProductInfo *ilh_smart_routing.ForecastLHProductInfo
	for i := range blockResult.ProductInfos {
		if blockResult.ProductInfos[i].ProductID == result.MultiProductID {
			foundProductInfo = &blockResult.ProductInfos[i]
			break
		}
	}

	if foundProductInfo == nil {
		// 创建新的产品信息
		productInfo := ilh_smart_routing.ForecastLHProductInfo{
			ProductID:                result.MultiProductID,
			ProductName:              productNameMap[int64(result.MultiProductID)],
			ReservedBSA:              0, // Block记录没有预留容量
			ReservedBSAProportion:    0,
			NonReservedBSA:           0, // Block记录没有非预留容量
			NonReservedBSAProportion: 0,
			AdhocWeight:              totalWeight, // 所有重量都是adhoc
			AdhocWeightProportion:    100,         // 比例为100%
		}
		blockResult.ProductInfos = append(blockResult.ProductInfos, productInfo)
	} else {
		// 更新现有产品信息
		foundProductInfo.AdhocWeight += totalWeight
		foundProductInfo.AdhocWeightProportion = 100 // 比例始终为100%
	}

	// 将更新后的结果存回map
	resultData.lhResultMap[blockResultKey] = blockResult
}

// processProductResult 处理产品结果
// 处理单条记录的产品相关信息，填充到临时数据结构中
func (s *ILHForecastTaskServiceImpl) processProductResult(
	ctx context.Context,
	result *persistent.ILHForecastTaskResultTab,
	ilhLine ilh_smart_routing.ILHLineInfo,
	importILHInfo ilh_smart_routing.ILHLineInfo,
	productNameMap map[int64]string,
	resultData *forecastResultData,
) {
	// 处理产品结果
	ilhProductID, ilhProductName := s.getILHProductInfo(ctx, result.MultiProductID)
	productResult, productExists := resultData.productResultMap[result.MultiProductID]
	if !productExists {
		productResult = ilh_smart_routing.ForecastProductResult{
			MultiProductID:   result.MultiProductID,
			MultiProductName: productNameMap[int64(result.MultiProductID)],
			ILHProductID:     ilhProductID,
			ILHProductName:   ilhProductName,
			Rules:            make([]ilh_smart_routing.ForecastProductRuleInfo, 0),
		}
	}

	// 处理规则信息
	ruleKey := generateRuleInfoKey(result.AvailableLHRuleName, result.MultiProductID)
	ruleInfo, ruleExists := resultData.ruleInfoMap[ruleKey]
	if !ruleExists {
		ruleInfo = ilh_smart_routing.ForecastProductRuleInfo{
			RuleID:   result.ID, // 简化处理，可能需要实际规则ID
			RuleName: result.AvailableLHRuleName,
			Priority: result.AvailableLHRulePriority,
			ILHLines: make([]ilh_smart_routing.ForecastProductRuleLineInfo, 0),
		}
		resultData.ruleInfoMap[ruleKey] = ruleInfo

		// 添加规则到产品结果
		productResult.Rules = append(productResult.Rules, ruleInfo)
		resultData.productResultMap[result.MultiProductID] = productResult
	}

	// 处理线路信息并更新产品结果
	s.processLineInfo(result, ilhLine, importILHInfo, &productResult, ruleKey, resultData)

	// 保存更新后的产品结果
	resultData.productResultMap[result.MultiProductID] = productResult
}

// processLineInfo 处理线路信息
// 处理单条记录的线路相关信息，填充到产品结果中
func (s *ILHForecastTaskServiceImpl) processLineInfo(
	result *persistent.ILHForecastTaskResultTab,
	ilhLine ilh_smart_routing.ILHLineInfo,
	importILHInfo ilh_smart_routing.ILHLineInfo,
	productResult *ilh_smart_routing.ForecastProductResult,
	ruleKey string,
	resultData *forecastResultData,
) {
	// 计算总分配重量
	totalWeight := result.ReversedBSAWeight + result.NonReversedBSAWeight + result.AdhocWeight

	// 创建线路信息，暂时不计算比例
	lineInfo := ilh_smart_routing.ForecastProductRuleLineInfo{
		ILHLine:             ilhLine,
		ImportILH:           importILHInfo,
		DGType:              result.DgType,
		TotalAssignedWeight: totalWeight,
		WeightProportion:    0, // 初始值为0，后续再计算
		AssignedBSAWeight:   result.ReversedBSAWeight + result.NonReversedBSAWeight,
		AssignedAdhocWeight: result.AdhocWeight,
	}

	// 添加线路信息到规则
	for i := range productResult.Rules {
		if productResult.Rules[i].RuleName == result.AvailableLHRuleName {
			ruleInfo := productResult.Rules[i]
			ruleInfo.ILHLines = append(ruleInfo.ILHLines, lineInfo)
			productResult.Rules[i] = ruleInfo

			// 更新ruleInfoMap
			resultData.ruleInfoMap[ruleKey] = ruleInfo
			break
		}
	}
}

// calculateWeightProportions 计算各线路重量比例
// 在所有数据收集完毕后计算线路重量占比
func (s *ILHForecastTaskServiceImpl) calculateWeightProportions(resultData *forecastResultData) {
	// 遍历所有产品
	for productID, productResult := range resultData.productResultMap {
		// 遍历产品下的每个规则
		for ruleIndex, rule := range productResult.Rules {
			// 计算规则下所有线路的总重量
			var ruleTotalWeight float64
			for _, lineInfo := range rule.ILHLines {
				ruleTotalWeight += lineInfo.TotalAssignedWeight
			}

			// 更新每条线路的比例
			for lineIndex, lineInfo := range rule.ILHLines {
				if ruleTotalWeight > 0 {
					// 计算比例
					weightProportion := calculateProportion(float64(lineInfo.TotalAssignedWeight), float64(ruleTotalWeight))

					// 更新线路信息的比例
					rule.ILHLines[lineIndex].WeightProportion = weightProportion
				} else {
					// 如果总重量为0，设置为100%
					rule.ILHLines[lineIndex].WeightProportion = 100
				}
			}

			// 更新规则
			productResult.Rules[ruleIndex] = rule
		}

		// 更新产品结果
		resultData.productResultMap[productID] = productResult
	}
}

// buildCCResult 构建CC结果
func (s *ILHForecastTaskServiceImpl) buildCCResult(
	ctx context.Context,
	resultData *forecastResultData,
	originalResults []persistent.ILHForecastTaskResultTab,
	ruleMinWeightMap map[string]float64,
	productNameMap map[int64]string,
	importILHInfo ilh_smart_routing.ILHLineInfo,
	destPort string,
	dgType ruleEntity.DGFlag,
) ilh_smart_routing.ForecastCCResult {
	// 初始化CC结果结构
	ccResult := s.initCCResult(importILHInfo, destPort, dgType)

	// 聚合处理数据
	ccProductAgg, exportILHInfoMap, exportLineTotalWeight := s.aggregateCCData(
		ctx,
		importILHInfo,
		destPort,
		dgType,
		originalResults,
		ruleMinWeightMap,
		productNameMap,
	)

	// 构建最终的ILHLines列表并计算比例
	s.buildFinalCCLines(&ccResult, ccProductAgg, exportILHInfoMap, exportLineTotalWeight)

	return ccResult
}

// initCCResult 初始化CC结果结构
func (s *ILHForecastTaskServiceImpl) initCCResult(
	importILHInfo ilh_smart_routing.ILHLineInfo,
	destPort string,
	dgType ruleEntity.DGFlag,
) ilh_smart_routing.ForecastCCResult {
	return ilh_smart_routing.ForecastCCResult{
		ImportILH:       importILHInfo,
		DestinationPort: destPort,
		DGType:          dgType,
		ILHLines:        make([]ilh_smart_routing.ForecastCCILHLineInfo, 0),
	}
}

// aggregateCCData 聚合CC数据
func (s *ILHForecastTaskServiceImpl) aggregateCCData(
	ctx context.Context,
	importILHInfo ilh_smart_routing.ILHLineInfo,
	destPort string,
	dgType ruleEntity.DGFlag,
	originalResults []persistent.ILHForecastTaskResultTab,
	ruleMinWeightMap map[string]float64,
	productNameMap map[int64]string,
) (
	map[string]map[int]*ilh_smart_routing.CCProductResult,
	map[string]ilh_smart_routing.ILHLineInfo,
	map[string]float64,
) {
	// 临时聚合结构
	ccProductAgg := make(map[string]map[int]*ilh_smart_routing.CCProductResult) // key1: Export ILH Line ID, key2: MultiProductID
	exportILHInfoMap := make(map[string]ilh_smart_routing.ILHLineInfo)          // key: Export ILH Line ID
	exportLineTotalWeight := make(map[string]float64)                           // key: Export ILH Line ID

	// 处理Block记录或常规记录
	if importILHInfo.LineID == BlockedLineID {
		// 特殊处理Block记录
		s.aggregateBlockedCCData(originalResults, ccProductAgg, exportILHInfoMap, exportLineTotalWeight, productNameMap)
	} else {
		// 遍历原始结果进行聚合
		for _, result := range originalResults {
			// 获取并验证航线信息
			laneInfo, err := s.laneService.GetLaneInfoByLaneCode(ctx, result.LaneCode)
			if err != nil {
				logger.CtxLogErrorf(ctx, "buildCCResult: Failed to get lane info for laneCode %s, skipping result: %v",
					result.LaneCode, err)
				continue
			}

			exportILHLine := laneInfo.GetCILHLineInfo()
			importILHLine := laneInfo.GetImportILHLineInfo()

			if laneInfo == nil || !s.isValidCCResult(importILHInfo, destPort, dgType, result, importILHLine, exportILHLine) {
				continue
			}

			// 提取关键数据
			exportLineID := exportILHLine.LineID
			multiProductID := result.MultiProductID
			itemWeight := result.ReversedBSAWeight + result.NonReversedBSAWeight + result.AdhocWeight
			ruleName := result.AvailableLHRuleName

			// 验证规则名称
			if ruleName == "" {
				logger.CtxLogErrorf(ctx, "buildCCResult: Rule name is empty in result record. Cannot determine MinRequiredWeight. TaskID: %d, ProductID: %d, LaneCode: %s",
					result.TaskID, multiProductID, result.LaneCode)
				continue
			}

			// 获取最小重量要求
			ccLineID := importILHLine.LineID
			minWeight := s.getMinRequiredWeight(ctx, multiProductID, ruleName, ccLineID, ruleMinWeightMap)

			// 更新总重量
			exportLineTotalWeight[exportLineID] += itemWeight

			// 初始化数据结构（如需）
			s.initExportLineMap(exportLineID, exportILHLine, ccProductAgg, exportILHInfoMap)

			// 聚合产品信息
			s.aggregateProductInfo(exportLineID, multiProductID, itemWeight, minWeight, productNameMap, ccProductAgg)
		}
	}

	return ccProductAgg, exportILHInfoMap, exportLineTotalWeight
}

// aggregateBlockedCCData 处理Block记录的CC数据聚合
func (s *ILHForecastTaskServiceImpl) aggregateBlockedCCData(
	originalResults []persistent.ILHForecastTaskResultTab,
	ccProductAgg map[string]map[int]*ilh_smart_routing.CCProductResult,
	exportILHInfoMap map[string]ilh_smart_routing.ILHLineInfo,
	exportLineTotalWeight map[string]float64,
	productNameMap map[int64]string,
) {
	// 为Block记录创建虚拟导出线路（与进口线路相同）
	exportILHInfoMap[BlockedLineID] = ilh_smart_routing.ILHLineInfo{
		LineID:   BlockedLineID,
		LineName: BlockedLineID,
	}
	ccProductAgg[BlockedLineID] = make(map[int]*ilh_smart_routing.CCProductResult)

	// 初始化总重量为0
	exportLineTotalWeight[BlockedLineID] = 0

	// 遍历所有Block记录，聚合产品信息
	for _, result := range originalResults {
		if isBlockedRecord(result.LaneCode) {
			multiProductID := result.MultiProductID
			// 对于Block记录，所有重量都存储在AdhocWeight中
			itemWeight := result.AdhocWeight

			// 处理产品聚合
			if existingResult, exists := ccProductAgg[BlockedLineID][multiProductID]; !exists {
				// 如果产品不存在，创建新的产品结果
				productName := ""
				if name, ok := productNameMap[int64(multiProductID)]; ok {
					productName = name
				}

				newResult := &ilh_smart_routing.CCProductResult{
					ProductID:         multiProductID,
					ProductName:       productName,
					AssignedWeight:    itemWeight,
					MinRequiredWeight: 0,    // Block记录没有最小重量要求
					MetMinimumVolume:  true, // 对Block记录，认为总是满足要求
					WeightProportion:  100,  // 比例将在最后计算
				}
				ccProductAgg[BlockedLineID][multiProductID] = newResult
				// 累计总重量
				exportLineTotalWeight[BlockedLineID] += itemWeight
			} else {
				// 更新已存在的产品信息
				existingResult.AssignedWeight += itemWeight
				exportLineTotalWeight[BlockedLineID] += itemWeight
			}
		}
	}
}

// isValidCCResult 验证是否属于当前CC结果范围
func (s *ILHForecastTaskServiceImpl) isValidCCResult(
	importILHInfo ilh_smart_routing.ILHLineInfo,
	destPort string,
	dgType ruleEntity.DGFlag,
	result persistent.ILHForecastTaskResultTab,
	importILHLine *lane_entity.LineInfo,
	exportILHLine *lane_entity.LineInfo,
) bool {
	// 特殊处理Block记录
	if importILHInfo.LineID == BlockedLineID {
		// 对于Block记录，我们只检查DG类型和LaneCode
		return isBlockedRecord(result.LaneCode) && result.DgType == dgType
	}

	// 常规记录的验证逻辑
	return importILHLine != nil &&
		importILHLine.LineID == importILHInfo.LineID &&
		result.DestinationPort == destPort &&
		result.DgType == dgType &&
		exportILHLine != nil &&
		exportILHLine.LineID != ""
}

// getMinRequiredWeight 获取最小重量要求
func (s *ILHForecastTaskServiceImpl) getMinRequiredWeight(
	ctx context.Context,
	multiProductID int,
	ruleName string,
	ccLineID string,
	ruleMinWeightMap map[string]float64,
) float64 {
	lookupKey := fmt.Sprintf("%d_%s_%s", multiProductID, ruleName, ccLineID)
	minWeight := float64(0) // 默认值

	if mw, exists := ruleMinWeightMap[lookupKey]; exists {
		minWeight = mw
	} else {
		logger.CtxLogErrorf(ctx, "buildCCResult: MinWeight lookup failed for key '%s'. Using default 0.", lookupKey)
	}

	return minWeight
}

// initExportLineMap 初始化导出线路的映射
func (s *ILHForecastTaskServiceImpl) initExportLineMap(
	exportLineID string,
	exportILHLine *lane_entity.LineInfo,
	ccProductAgg map[string]map[int]*ilh_smart_routing.CCProductResult,
	exportILHInfoMap map[string]ilh_smart_routing.ILHLineInfo,
) {
	if _, exists := ccProductAgg[exportLineID]; !exists {
		ccProductAgg[exportLineID] = make(map[int]*ilh_smart_routing.CCProductResult)
		exportILHInfoMap[exportLineID] = ilh_smart_routing.ILHLineInfo{
			LineID:   exportILHLine.LineID,
			LineName: exportILHLine.LineName,
		}
	}
}

// aggregateProductInfo 聚合产品信息
func (s *ILHForecastTaskServiceImpl) aggregateProductInfo(
	exportLineID string,
	multiProductID int,
	itemWeight float64,
	minWeight float64,
	productNameMap map[int64]string,
	ccProductAgg map[string]map[int]*ilh_smart_routing.CCProductResult,
) {
	if existingResult, exists := ccProductAgg[exportLineID][multiProductID]; !exists {
		// 首次遇到该产品
		productName := productNameMap[int64(multiProductID)]
		newResult := &ilh_smart_routing.CCProductResult{
			ProductID:         multiProductID,
			ProductName:       productName,
			AssignedWeight:    itemWeight,
			MinRequiredWeight: minWeight,
			MetMinimumVolume:  itemWeight >= minWeight,
			WeightProportion:  0, // 稍后计算
		}
		ccProductAgg[exportLineID][multiProductID] = newResult
	} else {
		// 更新已存在的产品信息
		existingResult.AssignedWeight += itemWeight
		existingResult.MetMinimumVolume = existingResult.AssignedWeight >= existingResult.MinRequiredWeight
	}
}

// buildFinalCCLines 构建最终的CC线路列表并计算比例
func (s *ILHForecastTaskServiceImpl) buildFinalCCLines(
	ccResult *ilh_smart_routing.ForecastCCResult,
	ccProductAgg map[string]map[int]*ilh_smart_routing.CCProductResult,
	exportILHInfoMap map[string]ilh_smart_routing.ILHLineInfo,
	exportLineTotalWeight map[string]float64,
) {
	for exportLineID, productMap := range ccProductAgg {
		ccILHLine := ilh_smart_routing.ForecastCCILHLineInfo{
			ILHLine:       exportILHInfoMap[exportLineID],
			ProductResult: make([]ilh_smart_routing.CCProductResult, 0, len(productMap)),
		}

		// 计算每个产品的重量比例
		totalLineWeight := exportLineTotalWeight[exportLineID]
		for _, ccProdResult := range productMap {
			ccProdResult.WeightProportion = calculateProportion(ccProdResult.AssignedWeight, totalLineWeight)
			ccILHLine.ProductResult = append(ccILHLine.ProductResult, *ccProdResult)
		}

		ccResult.ILHLines = append(ccResult.ILHLines, ccILHLine)
	}
}

// 计算比例（百分比）
func calculateProportion(part float64, total float64) int {
	if total == 0 {
		return 0
	}
	return int((part / total) * 100)
}

// Deploy 部署ILH预测任务
func (s *ILHForecastTaskServiceImpl) Deploy(ctx context.Context, req *ilh_smart_routing.DeployILHForecastTaskReq) (*ilh_smart_routing.DeployILHForecastTaskResp, *srerr.Error) {
	// 检查任务是否存在
	task, err := s.ilhForecastTaskRepo.GetILHForecastTaskByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 检查任务状态，只有 Done 状态的任务可以部署
	if task.TaskStatus != persistent.TaskStatusDone {
		return nil, srerr.New(srerr.TaskNotAllowDeploy, nil, "only done task can be deployed")
	}

	// 获取部署时间
	effectiveStartTime := req.EffectiveStartTime
	if req.EffectiveImmediately {
		effectiveStartTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}

	// 更新任务的部署状态
	task.DeployStatus = persistent.DeployStatusInEffect
	task.DeployTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	task.DeployOperator, _ = apiutil.GetUserInfo(ctx)

	// 保存任务状态
	if err := s.ilhForecastTaskRepo.UpdateILHForecastTask(ctx, task); err != nil {
		return nil, err
	}

	// 从ForecastLHCapacityTab复制到LHCapacityTab
	forecastCapacities, err := s.lhCapacityRepo.GetForecastLHCapacitiesByTaskID(ctx, req.ID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get forecast LH capacities: taskId=%d, err=%v", req.ID, err)
		return nil, err
	}

	// 如果存在容量配置，则部署到实际环境
	if len(forecastCapacities) > 0 {
		operator, _ := apiutil.GetUserInfo(ctx)
		for _, forecastCapacity := range forecastCapacities {
			// 直接创建实体对象
			capacity := &entity.LHCapacityConfig{
				CapacityName:       forecastCapacity.CapacityName,
				ILHVendorName:      forecastCapacity.ILHVendorName,
				ILHLineID:          forecastCapacity.ILHLineID,
				ILHLineName:        forecastCapacity.ILHLineName,
				TWS:                forecastCapacity.TWS,
				DestinationPort:    forecastCapacity.DestinationPort,
				DGType:             forecastCapacity.DGType,
				CapacitySettings:   forecastCapacity.CapacitySettings,
				RuleStatus:         ruleEntity.RuleStatusQueuing,
				EffectiveStartTime: effectiveStartTime,
				Operator:           operator,
			}

			// 保存到实际环境
			_, err := s.lhCapacityRepo.CreateLHCapacity(ctx, capacity)
			if err != nil {
				logger.CtxLogErrorf(ctx, "Failed to deploy forecast LH capacity: taskId=%d, capacityName=%s, err=%v",
					req.ID, forecastCapacity.CapacityName, err)
				return nil, err
			}
		}
	}

	// 从ForecastAvailableLHTab复制到AvailableLHTab
	forecastRulesList, err := s.availableLHRepo.GetForecastAvailableLHRulesByTaskID(ctx, req.ID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get forecast available LH rules: taskId=%d, err=%v", req.ID, err)
		return nil, err
	}

	// 如果存在预测规则，则部署到实际环境
	if forecastRulesList != nil && len(forecastRulesList) > 0 {
		operator, _ := apiutil.GetUserInfo(ctx)

		for _, forecastRules := range forecastRulesList {
			if forecastRules != nil && len(forecastRules.Rules) > 0 {
				// 创建新的AvailableLHTab实例
				availableLHRules := &availableLHEntity.AvailableLH{
					MultiProductID:     forecastRules.MultiProductID,
					RuleStatus:         ruleEntity.RuleStatusQueuing,
					EffectiveStartTime: effectiveStartTime,
					Rules:              forecastRules.Rules,
					Operator:           operator,
				}

				// 保存到实际环境
				if _, err := s.availableLHRepo.CreateAvailableLH(ctx, availableLHRules); err != nil {
					logger.CtxLogErrorf(ctx, "Failed to deploy forecast available LH rules: taskId=%d, multiProductID=%d, err=%v",
						req.ID, forecastRules.MultiProductID, err)
					return nil, err
				}
			}
		}
	}

	return &ilh_smart_routing.DeployILHForecastTaskResp{}, nil
}

// UpdateTaskStatus 更新ILH预测任务状态
func (s *ILHForecastTaskServiceImpl) UpdateTaskStatus(ctx context.Context, taskID int, taskStatus persistent.TaskStatus) *srerr.Error {
	// 获取任务信息
	task, err := s.ilhForecastTaskRepo.GetILHForecastTaskByID(ctx, taskID)
	if err != nil {
		return err
	}

	// 更新任务状态
	task.TaskStatus = taskStatus

	// 如果任务状态变为完成，更新完成时间和CompleteTime字段
	if taskStatus == persistent.TaskStatusDone {
		task.CompleteTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}

	// 保存任务状态
	if err := s.ilhForecastTaskRepo.UpdateILHForecastTask(ctx, task); err != nil {
		return err
	}

	return nil
}

// UpdateDeployStatus 更新ILH预测任务部署状态
func (s *ILHForecastTaskServiceImpl) UpdateDeployStatus(ctx context.Context, taskID int, deployStatus persistent.DeployStatus) *srerr.Error {
	// 获取任务信息
	task, err := s.ilhForecastTaskRepo.GetILHForecastTaskByID(ctx, taskID)
	if err != nil {
		return err
	}

	// 更新部署状态
	task.DeployStatus = deployStatus

	// 保存任务状态
	if err := s.ilhForecastTaskRepo.UpdateILHForecastTask(ctx, task); err != nil {
		return err
	}

	return nil
}

// Export 导出ILH预测任务结果
func (s *ILHForecastTaskServiceImpl) Export(ctx context.Context, req *ilh_smart_routing.ExportILHForecastTaskResultReq) (*ilh_smart_routing.ExportILHForecastTaskResultResp, *srerr.Error) {
	// 调用仓储层获取任务结果
	result, err := s.GetResult(ctx, &ilh_smart_routing.GetILHForecastTaskResultReq{ID: req.ID})
	if err != nil {
		return nil, err
	}

	// 准备Excel文件数据
	fileInfoList := s.prepareExcelData(result)

	// 生成Excel文件
	excelFile, fErr := fileutil.MakeExcelWithMultiSheet(ctx, fileInfoList)
	if fErr != nil {
		return nil, srerr.With(srerr.MakeExcelError, nil, fErr)
	}

	// 写入缓冲区
	buffer, wErr := excelFile.WriteToBuffer()
	if wErr != nil {
		return nil, srerr.With(srerr.SaveExcelErr, nil, wErr)
	}

	// 上传到S3
	fileURL, uploadErr := s.uploadExcelToS3(ctx, req.ID, buffer)
	if uploadErr != nil {
		return nil, uploadErr
	}

	return &ilh_smart_routing.ExportILHForecastTaskResultResp{
		URL: fileURL,
	}, nil
}

// prepareExcelData 准备Excel文件数据
func (s *ILHForecastTaskServiceImpl) prepareExcelData(result *ilh_smart_routing.GetILHForecastTaskResultResp) []*fileutil.FileInfo {
	return []*fileutil.FileInfo{
		s.prepareLHResultsSheet(result.LHResults),
		s.prepareCCResultsSheet(result.CCResults),
		s.prepareProductResultsSheet(result.ProductResults),
	}
}

// prepareLHResultsSheet 准备LH结果Sheet
func (s *ILHForecastTaskServiceImpl) prepareLHResultsSheet(lhResults []ilh_smart_routing.ForecastLHResult) *fileutil.FileInfo {
	header := []string{"Line ID", "Line Name", "Destination Port", "DG Type", "Product ID", "Product Name", "Reserved BSA Weight(kg)", "Reserved BSA Proportion", "Non-Reserved BSA Weight(kg)", "Non-Reserved BSA Proportion", "Adhoc Weight(kg)", "Adhoc Weight Proportion"}

	data := make([][]string, 0)
	for _, lhResult := range lhResults {
		for _, productInfo := range lhResult.ProductInfos {
			data = append(data, []string{
				lhResult.ILHLine.LineID,
				lhResult.ILHLine.LineName,
				lhResult.DestinationPort,
				lhResult.DGType.String(),
				strconv.Itoa(productInfo.ProductID),
				productInfo.ProductName,
				str.ToStr(productInfo.ReservedBSA),
				strconv.Itoa(productInfo.ReservedBSAProportion),
				str.ToStr(productInfo.NonReservedBSA),
				strconv.Itoa(productInfo.NonReservedBSAProportion),
				str.ToStr(productInfo.AdhocWeight),
				strconv.Itoa(productInfo.AdhocWeightProportion),
			})
		}
	}

	return &fileutil.FileInfo{
		SheetName: "LH Results",
		Header:    header,
		Data:      data,
	}
}

// prepareCCResultsSheet 准备CC结果Sheet
func (s *ILHForecastTaskServiceImpl) prepareCCResultsSheet(ccResults []ilh_smart_routing.ForecastCCResult) *fileutil.FileInfo {
	header := []string{"Import ILH Line ID", "Import ILH Line Name", "Destination Port", "DG Type", "ILH Line ID", "ILH Line Name", "Product ID", "Product Name", "Assigned Weight(kg)", "Weight Proportion", "Minimum Required Weight(kg)", "Met Minimum Volume"}

	data := make([][]string, 0)
	for _, ccResult := range ccResults {
		for _, ilhLine := range ccResult.ILHLines {
			for _, productResult := range ilhLine.ProductResult {
				metMinimumVolume := "No"
				if productResult.MetMinimumVolume {
					metMinimumVolume = "Yes"
				}
				data = append(data, []string{
					ccResult.ImportILH.LineID,
					ccResult.ImportILH.LineName,
					ccResult.DestinationPort,
					ccResult.DGType.String(),
					ilhLine.ILHLine.LineID,
					ilhLine.ILHLine.LineName,
					strconv.Itoa(productResult.ProductID),
					productResult.ProductName,
					str.ToStr(productResult.AssignedWeight),
					strconv.Itoa(productResult.WeightProportion),
					str.ToStr(productResult.MinRequiredWeight),
					metMinimumVolume,
				})
			}
		}
	}

	return &fileutil.FileInfo{
		SheetName: "CC Results",
		Header:    header,
		Data:      data,
	}
}

// prepareProductResultsSheet 准备产品结果Sheet
func (s *ILHForecastTaskServiceImpl) prepareProductResultsSheet(productResults []ilh_smart_routing.ForecastProductResult) *fileutil.FileInfo {
	header := []string{"Multi-Product ID", "Multi-Product Name", "ILH Product ID", "ILH Product Name", "Rule ID", "Rule Name", "Priority", "ILH Line ID", "ILH Line Name", "Import ILH Line ID", "Import ILH Line Name", "DG Type", "Total Assigned Weight(kg)", "Weight Proportion", "Assigned BSA Weight(kg)", "Assigned Adhoc Weight(kg)"}

	data := make([][]string, 0)
	for _, productResult := range productResults {
		for _, rule := range productResult.Rules {
			for _, ilhLine := range rule.ILHLines {
				data = append(data, []string{
					strconv.Itoa(productResult.MultiProductID),
					productResult.MultiProductName,
					productResult.ILHProductID,
					productResult.ILHProductName,
					strconv.Itoa(rule.RuleID),
					rule.RuleName,
					strconv.Itoa(rule.Priority),
					ilhLine.ILHLine.LineID,
					ilhLine.ILHLine.LineName,
					ilhLine.ImportILH.LineID,
					ilhLine.ImportILH.LineName,
					ilhLine.DGType.String(),
					str.ToStr(ilhLine.TotalAssignedWeight),
					strconv.Itoa(ilhLine.WeightProportion),
					str.ToStr(ilhLine.AssignedBSAWeight),
					str.ToStr(ilhLine.AssignedAdhocWeight),
				})
			}
		}
	}

	return &fileutil.FileInfo{
		SheetName: "Product Results",
		Header:    header,
		Data:      data,
	}
}

// uploadExcelToS3 将Excel文件上传到S3并返回URL
func (s *ILHForecastTaskServiceImpl) uploadExcelToS3(ctx context.Context, taskID int, buffer *bytes.Buffer) (string, *srerr.Error) {
	bucket := fileutil.GetBucketName(time.Now().Unix())
	s3Key := fmt.Sprintf("ILH-Forecast-Task-Result-%d-%s.xlsx", taskID, time.Now().Format("20060102150405"))

	if err := fileutil.Upload(ctx, bucket, s3Key, buffer); err != nil {
		return "", srerr.With(srerr.S3UploadFail, nil, err)
	}

	return fileutil.GetS3Url(ctx, bucket, s3Key), nil
}

// BatchSaveResults 批量保存ILH预测任务结果
func (s *ILHForecastTaskServiceImpl) BatchSaveResults(ctx context.Context, results []*forecastentity.ILHForecastResult) *srerr.Error {
	if len(results) == 0 {
		return nil
	}

	// 将领域对象转换为持久化对象
	persistentResults := make([]*persistent.ILHForecastTaskResultTab, 0, len(results))
	for _, result := range results {
		persistentResults = append(persistentResults, &persistent.ILHForecastTaskResultTab{
			TaskID:                  result.TaskID,
			AvailableLHRuleName:     result.AvailableLHRuleName,
			AvailableLHRulePriority: result.AvailableLHRulePriority,
			MultiProductID:          result.MultiProductID,
			LaneCode:                result.LaneCode,
			DestinationPort:         result.DestinationPort,
			DgType:                  result.DGType,
			ReversedBSAWeight:       result.ReversedBSAWeight / 1000,    // G转换为KG
			NonReversedBSAWeight:    result.NonReversedBSAWeight / 1000, // G转换为KG
			AdhocWeight:             result.AdhocWeight / 1000,          // G转换为KG
		})
	}

	// 调用仓储层的方法批量保存预测结果
	return s.ilhForecastTaskRepo.BatchSaveILHForecastResults(ctx, persistentResults)
}

func (s *ILHForecastTaskServiceImpl) getILHProductInfo(ctx context.Context, multiProductID int) (string, string) {
	ilhProductInfo, err := s.lpsApi.GetILHByMultiID(ctx, multiProductID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get ILH product info for multi product ID: multiProductID=%d, err=%v", multiProductID, err)
		return "", ""
	}

	return ilhProductInfo.IlhProductID, ilhProductInfo.IlhProductName
}

// processBlockedProductResult 处理被Block订单的产品结果
func (s *ILHForecastTaskServiceImpl) processBlockedProductResult(
	ctx context.Context,
	result *persistent.ILHForecastTaskResultTab,
	productNameMap map[int64]string,
	resultData *forecastResultData,
) {
	// 获取产品信息
	ilhProductID, ilhProductName := s.getILHProductInfo(ctx, result.MultiProductID)

	// 检查产品是否已存在
	productResult, productExists := resultData.productResultMap[result.MultiProductID]
	if !productExists {
		// 创建新的产品结果
		productResult = ilh_smart_routing.ForecastProductResult{
			MultiProductID:   result.MultiProductID,
			MultiProductName: productNameMap[int64(result.MultiProductID)],
			ILHProductID:     ilhProductID,
			ILHProductName:   ilhProductName,
			Rules:            make([]ilh_smart_routing.ForecastProductRuleInfo, 0),
		}
	}

	// 为Block记录创建特殊规则
	blockedRuleKey := fmt.Sprintf("%s_%d_%s", result.AvailableLHRuleName, result.MultiProductID, BlockedLineID)
	ruleInfo, ruleExists := resultData.ruleInfoMap[blockedRuleKey]
	if !ruleExists {
		// 创建Block规则
		ruleInfo = ilh_smart_routing.ForecastProductRuleInfo{
			RuleID:   result.ID,
			RuleName: result.AvailableLHRuleName,
			Priority: result.AvailableLHRulePriority,
			ILHLines: make([]ilh_smart_routing.ForecastProductRuleLineInfo, 0),
		}

		// 创建Block线路信息
		lineInfo := ilh_smart_routing.ForecastProductRuleLineInfo{
			ILHLine: ilh_smart_routing.ILHLineInfo{
				LineID:   BlockedLineID,
				LineName: BlockedLineID,
			},
			DGType:              result.DgType,
			TotalAssignedWeight: result.AdhocWeight, // Block记录的重量都在AdhocWeight中
			WeightProportion:    100,                // 比例始终为100%
			AssignedBSAWeight:   0,                  // Block记录没有BSA容量
			AssignedAdhocWeight: result.AdhocWeight, // 全部是Adhoc重量
		}

		// 添加线路信息到规则
		ruleInfo.ILHLines = append(ruleInfo.ILHLines, lineInfo)

		// 存储规则信息
		resultData.ruleInfoMap[blockedRuleKey] = ruleInfo

		// 添加规则到产品结果
		productResult.Rules = append(productResult.Rules, ruleInfo)
	} else {
		// 更新现有规则中的线路信息
		for i, line := range ruleInfo.ILHLines {
			if line.ILHLine.LineID == BlockedLineID && line.DGType == result.DgType {
				// 更新现有线路的重量
				ruleInfo.ILHLines[i].TotalAssignedWeight += result.AdhocWeight
				ruleInfo.ILHLines[i].AssignedAdhocWeight += result.AdhocWeight

				// 更新规则信息
				resultData.ruleInfoMap[blockedRuleKey] = ruleInfo

				// 更新产品规则
				for j, rule := range productResult.Rules {
					if rule.RuleName == ruleInfo.RuleName && rule.Priority == ruleInfo.Priority {
						productResult.Rules[j] = ruleInfo
						break
					}
				}
				break
			}
		}
	}

	// 保存更新后的产品结果
	resultData.productResultMap[result.MultiProductID] = productResult
}

// processBlockedImportILH 处理被Block订单的进口ILH结果
func (s *ILHForecastTaskServiceImpl) processBlockedImportILH(
	result *persistent.ILHForecastTaskResultTab,
	resultData *forecastResultData,
) {
	// 创建Block线路信息
	blockedImportILH := ilh_smart_routing.ILHLineInfo{
		LineID:   BlockedLineID,
		LineName: BlockedLineID,
	}

	// 存储到ImportILH映射
	resultData.importILHInfoMap[BlockedLineID] = blockedImportILH

	// 添加目的港和DG类型映射
	if _, exists := resultData.destPortDGMap[BlockedLineID]; !exists {
		resultData.destPortDGMap[BlockedLineID] = make(map[string]map[ruleEntity.DGFlag]bool)
	}

	// 使用常量作为被Block订单的目的港标识
	if _, exists := resultData.destPortDGMap[BlockedLineID][BlockedDestPort]; !exists {
		resultData.destPortDGMap[BlockedLineID][BlockedDestPort] = make(map[ruleEntity.DGFlag]bool)
	}

	// 记录DG类型
	resultData.destPortDGMap[BlockedLineID][BlockedDestPort][result.DgType] = true
}
