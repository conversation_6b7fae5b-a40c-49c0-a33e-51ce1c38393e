package wbcclient

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/jwtutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
)

type wbcHeader struct {
	requestId string
}

func WbcHeader(ctx context.Context) *wbcHeader {
	return &wbcHeader{requestId: requestid.GetFromCtx(ctx)}
}

func (p *wbcHeader) Header(ctx context.Context) (map[string]string, error) {
	apiConf := configutil.GetWaybillApi(ctx)
	secret := apiConf.Password
	jwtToken, err := jwtutil.BuildJWT(ctx, envvar.GetCID(), user, operator, secret)
	if err != nil {
		return nil, srerr.With(srerr.JwtTokenErr, nil, err)
	}
	return map[string]string{"jwt-token": jwtToken, "X-Request-Id": p.requestId}, nil
}

func (p *wbcHeader) SecondTimeout(ctx context.Context) int {
	return configutil.GetWaybillApi(ctx).Timeout
}
