package client

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mockutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/ratelimiter"
	"strings"
	"time"
)

type Scene string

const (
	MultiAlive Scene = "multi-alive"
	DR         Scene = "dr"
)

type GrpcService struct {
	OperationID string
	Scene       Scene
	System      string
}

var (
	_defaultGrpcInvoker = chassis.NewRPCInvoker()
	breakerErr          = errors.New("circuit breaker is open")
)

const (
	defaultGrpcDRTimeout         = time.Duration(6000) * time.Millisecond
	defaultGrpcMultiAliveTimeout = time.Duration(2000) * time.Millisecond
)

func GrpcInvoke(ctx context.Context, systemCode string, schemaID string, grpcService GrpcService, arg interface{}, reply interface{}) error {
	// set timeout
	operationID := grpcService.OperationID
	// for mock
	if target, _, ok := GrpcMockTarget(ctx, systemCode); ok {
		conn, err := GetGrpcConnWithSecure(ctx, target)
		if err != nil {
			logger.CtxLogErrorf(ctx, "grpc_invoke_mock|schema=%s,operation=%s,err=%v", schemaID, operationID, err)
			return err
		}
		defer conn.Close()
		schemaID = strings.TrimLeft(schemaID, "/")
		operationID = strings.TrimLeft(operationID, "/")
		method := fmt.Sprintf("/%s/%s", schemaID, operationID)
		err = conn.Invoke(ctx, method, arg, reply)
		if err != nil {
			logger.CtxLogErrorf(ctx, "grpc_invoke_mock|schema=%s,operation=%s,err=%v", schemaID, operationID, err)
			return err
		}
		logger.CtxLogDebugf(ctx, "grpc_invoke_mock|schema=%s,operation=%s,req=%s,resp=%s", schemaID, operationID, str.JsonStringForDebugLog(arg), str.JsonStringForDebugLog(reply))
		return nil
	}

	limitKey := objutil.Merge(systemCode, "/", schemaID, "/", operationID)
	if err := CheckGrpcFlow(ctx, limitKey, GrpcClient); err != nil {
		return err
	}

	timeout := getGrpcTimeout(ctx, systemCode, grpcService)
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	microServiceName := getServiceName(systemCode, envvar.GetEnvLower(ctx), envvar.GetCIDLower(), grpcService.Scene)
	if err := _defaultGrpcInvoker.Invoke(ctx, microServiceName, schemaID, operationID, arg, reply, chassis.WithProtocol("grpc")); err != nil {
		if strings.HasSuffix(err.Error(), context.DeadlineExceeded.Error()) {
			logger.CtxLogErrorf(ctx, "GrpcInvoke timeout,systemCode=%s, operationID=%s,timeout=%v, err=%v", systemCode, operationID, timeout, err)
			namespace := systemCode + "+" + operationID
			_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleGrpcTimeout, namespace, monitoring.StatusSuccess, fmt.Sprintf("timeout:%s", timeout))
		}
		logger.CtxLogErrorf(ctx, "grpc_invoke|microservice=%s,schema=%s,operation=%s,req=%s,err=%+v", microServiceName, schemaID, operationID, str.JsonString(arg), err)
		return err
	}
	logger.CtxLogInfof(ctx, "grpc_invoke|microservice=%s,schema=%s,operation=%s,req=%s,resp=%s", microServiceName, schemaID, operationID, str.JsonStringForDebugLog(arg), str.JsonStringForDebugLog(reply))
	return nil
}

func getGrpcTimeout(ctx context.Context, systemCode string, grpcService GrpcService) time.Duration {
	timeout := defaultGrpcDRTimeout
	if grpcService.Scene == MultiAlive {
		timeout = defaultGrpcMultiAliveTimeout
	}

	grpcConfig := configutil.GetLocationGrpcConf(ctx)
	switch systemCode {
	case constant.SystemChargeCheckoutGrpc, constant.SystemChargeCoreGrpc:
		grpcConfig = configutil.GetChargeGrpcConf(ctx)
	case constant.SystemLLS:
		grpcConfig = configutil.GetLlsGrpcConf(ctx)
	case constant.SystemLFS:
		grpcConfig = configutil.GetLfsGrpcConf(ctx)
	case constant.SystemLPSAdmin, constant.SystemLPS, constant.SystemLpsFulfillment:
		grpcConfig = configutil.GetLpsGrpcConf(ctx)
	case constant.SystemLCOS:
		grpcConfig = configutil.GetLcosGrpcConf(ctx)
	}

	if apiTimeout, ok := grpcConfig.TimeoutConfig[grpcService.OperationID]; ok {
		timeout = apiTimeout
	}

	return timeout
}

// env and cid must be lowercase
func getServiceName(systemCode, env, cid string, scene Scene) string {
	serviceName := ""
	if envvar.IsLivetest() {
		switch systemCode {
		case constant.SystemLFS:
			return fmt.Sprintf("lfs-livetestgrpc-%s-%s", strings.ToLower(env), strings.ToLower(cid))
		case constant.SystemLLS:
			return str.Merge("lls-grpc-", strings.ToLower(cid))
		case constant.SystemChargeCheckoutGrpc:
			serviceName = str.Join("-", "sls", "chargecheckoutapilivetest", strings.ToLower(env), strings.ToLower(cid))
			return serviceName
		case constant.SystemChargeCoreGrpc:
			serviceName = str.Join("-", "sls", "chargecoreapilivetest", strings.ToLower(env), strings.ToLower(cid))
			return serviceName
		case constant.SystemLocation:
			return fmt.Sprintf("sls-locationgrpclivetest-live-%s", strings.ToLower(cid))
		case constant.SystemLCOS:
			if scene == MultiAlive && envvar.IsLivetest() {
				serviceName = str.Join("-", "lcos", "checkout", strings.ToLower(cid))
			} else {
				serviceName = str.Join("-", "lcos", "grpc", strings.ToLower(cid))
			}
			return serviceName
		default:
			return ""
		}
	}

	switch systemCode {
	case constant.SystemLFS:
		return str.Join("-", "lfs", "grpc", strings.ToLower(env), strings.ToLower(cid))
	case constant.SystemLLS:
		return str.Join("-", "lls", "grpc", strings.ToLower(cid))
	//case constant.SystemLPS:
	//	return strings.ToLower(fmt.Sprintf("wbc-static-%s", cid))
	case constant.SystemLocation:
		return str.Join("-", "sls", "locationgrpc", strings.ToLower(env), strings.ToLower(cid))
	case constant.SystemChargeCheckoutGrpc:
		serviceName = str.Join("-", "sls", "chargecheckoutapi", strings.ToLower(env), strings.ToLower(cid))
		return serviceName
	case constant.SystemChargeCoreGrpc:
		serviceName = str.Join("-", "sls", "chargecoreapi", strings.ToLower(env), strings.ToLower(cid))
		return serviceName
	case constant.SystemLCOS:
		if scene == MultiAlive {
			serviceName = str.Join("-", "lcos", "checkout", strings.ToLower(cid))
		} else {
			serviceName = str.Join("-", "lcos", "grpc", strings.ToLower(cid))
		}
		return serviceName
	default:
		return ""
	}
}

func GrpcMockTarget(ctx context.Context, systemCode string) (string, string, bool) {
	if mockMap := mockutil.MockValues(ctx); len(mockMap) > 0 {
		if mockSys, ok := mockMap[mockutil.MockSystemsKey]; ok {
			systems := strings.Split(mockSys, ",")
			for _, system := range systems {
				if systemCode == system {
					conf := configutil.GetMockDomainConf(ctx)
					MockGrpcTarget := conf.GrpcDomain
					logger.CtxLogInfof(ctx, "GrpcMockTarget|SystemCode:%s|GrpcTarget:%s|MockRequestID:%s|Result:%v", systemCode, MockGrpcTarget, mockMap[mockutil.MockRequestID], true)
					return MockGrpcTarget, mockMap[mockutil.MockRequestID], true
				}
			}
		}
	}

	return "", "", false
}

// CheckGrpcFlow 对接口进行限流或熔断
// 入参：key：需要与Apollo配置的匹配， role：分为客户端和服务端，调用下游的grpc invoke的角色为客户端， select lane等接口的角色为服务端
func CheckGrpcFlow(ctx context.Context, key string, role string) error {
	// 判断是否需要熔断
	var breakerSwitch bool
	var globalSwitch bool
	if role == GrpcClient {
		breakerSwitch = configutil.GetGrpcBreakerConf(ctx).ClientConfigMap[key]
		globalSwitch = configutil.GetGrpcBreakerConf(ctx).GrpcClientGlobalSwitch
	}
	logger.CtxLogInfof(ctx, "CheckGrpcFlow|key:%v, role:%v, key is break:%v, global is break:%v", key, role, breakerSwitch, globalSwitch)
	if breakerSwitch || globalSwitch {
		return breakerErr
	}

	// 判断是否需要限流
	if !ratelimiter.CheckRateLimit(ctx, key) {
		return ratelimiter.QpsLimit
	}

	return nil
}
