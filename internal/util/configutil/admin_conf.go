package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	lpsAdminKey = "lps_admin"
)

type AdminConf struct {
	Host      string `yaml:"host"`
	JwtOptr   string `yaml:"jwt_optr"`
	JwtSecret string `yaml:"jwt_secret"`
	Timeout   int64  `yaml:"timeout"`
}

func refreshAdminConf(externalKey string) AdminConf {
	var conf AdminConf
	if err := config.UnmarshalConfig(configPrefix+externalKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh external jwt config fail, key:%s, err:%+v", externalKey, err)
	}
	return conf
}

func GetLpsAdminConf(ctx context.Context) AdminConf {
	return GetGlobalConfig(ctx).LpsJwtConf
}
