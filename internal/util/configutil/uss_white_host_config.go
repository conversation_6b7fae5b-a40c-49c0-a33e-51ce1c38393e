package configutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const ussWhiteHost = "uss_white_host"

func RefreshUssWhiteHost() []string {
	var hosts []string

	confStr := config.GetString(configPrefix+ussWhiteHost, "{}") // nolint
	if err := jsoniter.UnmarshalFromString(confStr, &hosts); err != nil {
		logger.LogErrorf("Unmarsha uss_white_host config failed|err=%v", err)
		return nil
	}

	return hosts
}

func GetUssWhiteHost() []string {
	if gConf.UssWhiteHost == nil {
		return []string{}
	}

	return gConf.UssWhiteHost
}
