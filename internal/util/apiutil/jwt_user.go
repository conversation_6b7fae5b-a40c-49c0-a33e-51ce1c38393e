package apiutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
)

func GetUserInfo(ctx context.Context) (string, int) {
	var userEmail string
	var userCategory int
	if email := ctx.Value(constant.EmailKey); email != nil {
		if val, ok := email.(string); ok {
			userEmail = val
		}
	}
	if cat := ctx.Value(constant.CategoryKey); cat != nil {
		if val, ok := cat.(int); ok {
			userCategory = val
		}
	}
	return userEmail, userCategory
}

func GetUserID(ctx context.Context) string {
	if id := ctx.Value(constant.RequestUserIDKey); id != nil {
		if userId, ok := id.(string); ok {
			return userId
		}
	}
	return ""
}

func max(a, b int) int {
	if a > b {
		return a
	} else {
		return b
	}
}

func GetOffsetAndLimit(pageNo, limit int) (int, int, int) {
	pageNo, offset := max(pageNo, 1), 0
	if limit <= 0 {
		limit = 20
	}
	offset = (pageNo - 1) * limit
	return pageNo, offset, limit
}
