package prometheusutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"strconv"
)

const (
	ScheduleRoutingNumMonitor                    = "Schedule_routing_num"
	ScheduleRoutingShippingFeeMonitor            = "Schedule_routing_shipping_fee"
	ScheduleAllocateNumMonitor                   = "Schedule_allocate_num"
	ScheduleAllocateShippingFeeMonitor           = "Schedule_allocate_shipping_fee"
	ScheduleRoutingLineFeeSuccessRateMonitor     = "Schedule_routing_line_fee_success_rate"
	ScheduleAllocateProductFeeSuccessRateMonitor = "Schedule_allocate_product_fee_success_rate"
)

func InitScheduleMonitor() {
	cm := map[string][]string{
		ScheduleRoutingNumMonitor:                    []string{"schedule_line_num"},
		ScheduleRoutingShippingFeeMonitor:            []string{"schedule_line"},
		ScheduleAllocateNumMonitor:                   []string{"schedule_product_num"},
		ScheduleAllocateShippingFeeMonitor:           []string{"schedule_product"},
		ScheduleRoutingLineFeeSuccessRateMonitor:     []string{"line_id", "rate_channel_id", "retcode"},
		ScheduleAllocateProductFeeSuccessRateMonitor: []string{"product_id", "retcode"},
	}

	for k, v := range cm {
		label := metrics.CounterOpts{
			Name:   k,
			Help:   "",
			Labels: v,
		}

		err := metrics.CreateCounter(label)
		if err != nil {
			logger.LogErrorf("init InitScheduleMonitor metrics failed %+v", err)
		}
	}
}

func RoutingShippingFeeReport(lineFeeMap map[string]float64) {
	for k, v := range lineFeeMap {
		if v < 0 {
			continue
		}
		_ = CounterIncr(ScheduleRoutingNumMonitor, map[string]string{
			"schedule_line_num": k,
		})

		_ = CounterAdd(ScheduleRoutingShippingFeeMonitor, map[string]string{
			"schedule_line": k,
		}, v)
	}
}

func AllocateShippingFeeReport(productId int64, fee float64) {
	_ = CounterIncr(ScheduleAllocateNumMonitor, map[string]string{
		"schedule_product_num": strconv.FormatInt(productId, 10),
	})

	_ = CounterAdd(ScheduleAllocateShippingFeeMonitor, map[string]string{
		"schedule_product": strconv.FormatInt(productId, 10),
	}, fee)
}

func ReportRoutingFeeSuccessRate(lineId string, rateChannelId int, retcode int) {
	_ = CounterIncr(ScheduleRoutingLineFeeSuccessRateMonitor, map[string]string{
		"line_id":         lineId,
		"rate_channel_id": strconv.Itoa(rateChannelId),
		"retcode":         strconv.Itoa(retcode),
	})
}

func ReportAllocateFeeSuccessRate(productId string, retcode int) {
	_ = CounterIncr(ScheduleAllocateProductFeeSuccessRateMonitor, map[string]string{
		"product_id": productId,
		"retcode":    strconv.Itoa(retcode),
	})
}
