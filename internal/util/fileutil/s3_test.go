package fileutil

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGetS3UrlByUrl(t *testing.T) {
	type args struct {
		ctx   context.Context
		s3Url string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "test-url",
			want: "true",
			args: args{
				ctx:   context.Background(),
				s3Url: "https://proxy.uss.s3.test.shopee.io/smart-routing-task-test-id-5/CB_Shop_Group%3A2024-05-13%2019%3A28%3A36.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=********%2F20240513%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20240513T122836Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=cda452baa1da6b5a20b3c2a52820325370e9cfe4daa23997c86466de667d5698",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, GetS3UrlByUrl(tt.args.ctx, tt.args.s3Url), "GetS3UrlByUrl(%v, %v)", tt.args.ctx, tt.args.s3Url)
		})
	}
}
