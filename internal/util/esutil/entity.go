package esutil

import (
	"bytes"
	"encoding/json"
	"git.garena.com/shopee/bg-logistics/go/go-elasticsearch/v7/esapi"
)

type EsRequest interface {
	GetRequestParam() (esapi.SearchRequest, error)
}

type SearchRequest struct {
	Index       string
	CurrentPage int
	PageSize    int
	Condition   map[string]interface{}
	OrderBy     []map[string]interface{}
}

func (s SearchRequest) GetRequestParam() (esapi.SearchRequest, error) {
	//1.创建请求体
	req := esapi.SearchRequest{}
	// 2.用map定义请求es的格式
	res := map[string]interface{}{
		"from": s.CurrentPage,
		"size": s.PageSize,
		"query": map[string]interface{}{
			"term": s.Condition,
		},
	}
	if s.OrderBy != nil {
		res["sort"] = s.OrderBy
	}
	// 3.序列化协议
	var buf bytes.Buffer
	if err := json.NewEncoder(&buf).Encode(res); err != nil {
		return req, err
	}

	req.Index = []string{s.Index}
	req.Body = &buf

	return req, nil
}

type EsResponse struct {
	Hits struct {
		Total struct {
			Value int `json:"value"`
		} `json:"total"`
		Hits []struct {
			Source json.RawMessage `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}
