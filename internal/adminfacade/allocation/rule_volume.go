package allocation

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
)

type MaskingRuleVolumeFacade struct {
	MaskRuleVolumeService rulevolume2.MaskRuleVolumeService
}

func (p *MaskingRuleVolumeFacade) URLPatterns() []restful.Route {
	adminGroup := restful.NewRouterGroup("/api/admin")

	maskingVolume := adminGroup.Group("/rule_volume")
	maskingVolume.GET("/list", p.ListView)
	maskingVolume.GET("/detail", p.DetailView)
	maskingVolume.POST("/create", p.CreateView)
	maskingVolume.POST("/update", p.UpdateView)
	maskingVolume.POST("/copy", p.CopyView)
	maskingVolume.POST("/delete", p.DeleteView)
	maskingVolume.POST("/check", p.CheckView)
	maskingVolume.POST("/export_check", p.ExportCheckView)

	return adminGroup.GetRouters()
}

func (p *MaskingRuleVolumeFacade) ListView(ctx *restful.Context) {
	req := &rulevolume2.GetRuleVolumeListRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, total, err := p.MaskRuleVolumeService.ListRuleVolumes(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp := &rulevolume2.GetRuleVolumeListResponse{
		List:   data,
		Pageno: req.PageNo,
		Count:  req.Limit,
		Total:  total,
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *MaskingRuleVolumeFacade) DetailView(ctx *restful.Context) {
	req := &rulevolume2.GetRuleVolumeByIDRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := p.MaskRuleVolumeService.GetRuleVolumeByID(ctx.Ctx, req.ID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, data)
}

func (p *MaskingRuleVolumeFacade) CreateView(ctx *restful.Context) {
	req := &rulevolume2.CreateRuleVolumeRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := req.Validate(ctx.Ctx); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := p.MaskRuleVolumeService.CreateRuleVolume(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, data)
}

func (p *MaskingRuleVolumeFacade) UpdateView(ctx *restful.Context) {
	req := &rulevolume2.UpdateRuleVolumeRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := req.Validate(ctx.Ctx); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := p.MaskRuleVolumeService.UpdateRuleVolume(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, data)
}

func (p *MaskingRuleVolumeFacade) CopyView(ctx *restful.Context) {
	req := &rulevolume2.CopyRuleVolumeRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := p.MaskRuleVolumeService.CopyRuleVolume(ctx.Ctx, req.ID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, data)
}

func (p *MaskingRuleVolumeFacade) DeleteView(ctx *restful.Context) {
	req := &rulevolume2.DeleteRuleVolumeRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := p.MaskRuleVolumeService.DeleteRuleVolume(ctx.Ctx, req.ID); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

// CheckView 对比已有的volume数据与当前配置的数据，只返回top 10
func (p *MaskingRuleVolumeFacade) CheckView(ctx *restful.Context) {
	req := &rulevolume2.CheckRuleVolumeRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// batch allocate 不需要check
	if req.AllocationMethod == allocation.BatchAllocate {
		apiutil.SuccessJSONResp(ctx, nil)
		return
	}
	// 未开启的市场不需要check
	conf := configutil.GetMaskVolumeCheckConf(ctx.Ctx)
	if !conf.AllowCheck {
		apiutil.SuccessJSONResp(ctx, nil)
		return
	}

	if resp, err := p.MaskRuleVolumeService.CheckRuleVolume(ctx.Ctx, req, false); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
	} else {
		apiutil.SuccessJSONResp(ctx, resp)
	}
}

// ExportCheckView 导出volume rule check时所有的对比数据
func (p *MaskingRuleVolumeFacade) ExportCheckView(ctx *restful.Context) {
	req := &rulevolume2.CheckRuleVolumeRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// batch allocate 不需要check
	if req.AllocationMethod == allocation.BatchAllocate {
		apiutil.SuccessJSONResp(ctx, nil)
		return
	}
	// 未开启的市场不需要check
	conf := configutil.GetMaskVolumeCheckConf(ctx.Ctx)
	if !conf.AllowCheck {
		apiutil.SuccessJSONResp(ctx, nil)
		return
	}

	if resp, err := p.MaskRuleVolumeService.ExportCheckRuleVolume(ctx.Ctx, req, true); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	} else {
		apiutil.SuccessJSONResp(ctx, resp)
		return
	}
}
