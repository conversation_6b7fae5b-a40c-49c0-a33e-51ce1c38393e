package allocation

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	batch_allocate2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	allocation3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/gogo/protobuf/proto"
	"runtime"
	"strconv"
)

const (
	defaultBatchLogSize = 1000
	//GreedyAlgo          = "GreedyAlgo"
	//ReBalance           = "ReBalance"
	//LocalSearch         = "LocalSearch"
	panicMsgSize = 4096
)

func (b *BatchAllocateServiceImpl) sendAllocationLogBytes(ctx context.Context, algoResp *algorithm_client.BatchAllocateRespBo,
	batchInfo batch_allocate2.BAOnlineBatchTab, orderInfoMap map[uint64]*allocation3.AllocateOrderInfoForLog, orders []*order.BatchAllocateHoldOrderTab) {
	defer func() {
		if err := recover(); err != nil {
			var buf [panicMsgSize]byte
			n := runtime.Stack(buf[:], false)
			errMsg := fmt.Sprintf("[Recovery]sendAllocationLogBytes panic recovered:\n%v\n%s", err, string(buf[:n]))
			logger.CtxLogErrorf(ctx, errMsg)
		}
	}()

	if len(algoResp.OrderResult) == 0 {
		return
	}

	startTimeStamp := timeutil.GetCurrentUnixTimeStamp(ctx)
	newOrderInfoMap := b.copyOrderInfoMap(ctx, orderInfoMap)
	logger.CtxLogInfof(ctx, "sendAllocationLogBytes|after copy order info,order map length:%d, cost time:%v", len(orderInfoMap), timeutil.GetCurrentUnixTimeStamp(ctx)-startTimeStamp)

	startTimeStamp = timeutil.GetCurrentUnixTimeStamp(ctx)
	distributions := b.countAlgoResp(ctx, algoResp)
	logger.CtxLogInfof(ctx, "sendAllocationLogBytes|after count algo resp,algo resp length:%d, cost time:%v", len(algoResp.OrderResult), timeutil.GetCurrentUnixTimeStamp(ctx)-startTimeStamp)

	// 直接分批，发送到task，task异步处理
	startTimeStamp = timeutil.GetCurrentUnixTimeStamp(ctx)
	orderLists := b.splitOrderResults(ctx, algoResp)
	productParcelInfoMap := make(map[uint64][]*pb.ProductParcelInfo)

	for _, orderInfo := range orders {
		orderID := orderInfo.OrderID
		if productParcelInfoMap[orderID] == nil {
			productParcelInfoMap[orderID] = make([]*pb.ProductParcelInfo, 0)
		}
		for _, product := range orderInfo.FulfillmentHardResult {
			productParcelInfoMap[orderID] = append(productParcelInfoMap[orderID], &pb.ProductParcelInfo{
				ProductId: proto.Int64(int64(product.FulfillmentProductId)),
				ParcelTypeAttr: &pb.ParcelTypeAttr{
					IsCod:       proto.Bool(product.ParcelAttribute.IsCod),
					IsHighValue: proto.Bool(product.ParcelAttribute.IsHighValue),
					IsBulky:     proto.Bool(product.ParcelAttribute.IsBulky),
					IsDg:        proto.Bool(product.ParcelAttribute.IsDg),
				},
			})
		}
	}

	for i := 0; i < len(orderLists); i++ {
		orderList := orderLists[i]
		marshalBytes := objutil.JsonBytes(orderList)
		list := make([]allocation3.BatchAllocationDetail, 0)
		for j := 0; j < len(orderList); j++ {
			list = append(list, allocation3.BatchAllocationDetail{
				ProductParcelInfoList: productParcelInfoMap[orderList[j].OrderId],
			})
		}

		tempLog := &allocation3.TempAllocationLog{
			Log: &allocation3.Log{
				OrderListBytes:        marshalBytes,
				Distributions:         distributions,
				MaskingProductId:      int64(batchInfo.MaskProductID),
				BatchInfoBytes:        objutil.JsonBytes(batchInfo),
				OrderInfoMapBytes:     objutil.JsonBytes(newOrderInfoMap),
				LastSecond:            b.logInfo.LastSecond,
				CountryVolumeMapBytes: objutil.JsonBytes(b.logInfo.CountryVolumeMap),
				BatchAllocationLog: &allocation3.BatchAllocationLog{
					List: list,
				},
			},
		}
		tempLogBytes := objutil.JsonBytes(tempLog)
		msg := zip.ZSTDCompress(tempLogBytes)

		namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
		if err := kafkahelper.DeliveryMessage(
			ctx, namespace, constant.TaskNameMakeUpAsyncAllocationLog, msg, nil, kafkahelper.BatchAllocationPathType,
		); err != nil {
			errMsg := fmt.Sprintf("delivery batch allocation order lists to kafka failed | err=%v", err)
			_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleBatchAllocate, "Batch Allocation Log", monitoring.StatusError, errMsg)
			logger.CtxLogErrorf(ctx, errMsg)
		}
	}
	logger.CtxLogInfof(ctx, "sendAllocationLogBytes|after send kafka, cost time:%v", timeutil.GetCurrentUnixTimeStamp(ctx)-startTimeStamp)
}

func (b *BatchAllocateServiceImpl) splitOrderResults(ctx context.Context, algoResp *algorithm_client.BatchAllocateRespBo) [][]*algorithm_client.OrderResultBo {
	// 拆分Order list
	// 初始化应该拆分的批次数
	size := defaultBatchLogSize
	conf := configutil.GetBatchAllocationLogConf(ctx)
	if conf.DefaultBatchLogSize != 0 {
		size = conf.DefaultBatchLogSize
	}
	batches := len(algoResp.OrderResult) / size
	if batches*size < len(algoResp.OrderResult) { //整除的情况下，batches只会小于或等于实际批次数
		batches += 1
	}
	orderLists := make([][]*algorithm_client.OrderResultBo, 0)
	for i := 0; i < batches; i++ {
		startIdx := i * size
		endIdx := (i + 1) * size
		if (i + 1) == batches { //最后一批
			endIdx = len(algoResp.OrderResult)
		}
		orderLists = append(orderLists, algoResp.OrderResult[startIdx:endIdx])
	}
	return orderLists
}

func (b *BatchAllocateServiceImpl) countAlgoResp(ctx context.Context, algoResp *algorithm_client.BatchAllocateRespBo) []allocation3.Distribution {
	countryCostVolume := make(map[int64]int64, 0)
	countryParcelCostVolume := make(map[string]int64, 0)
	allocateSfMap := make(map[int64]float64, 0)
	for i := 0; i < len(algoResp.OrderResult); i++ {
		orderResult := algoResp.OrderResult[i]
		productID := int64(orderResult.FulfillmentProductId)
		countryCostVolume[productID] += 1
		if stats, ok := algoResp.StatsResult[uint64(productID)]; ok {
			if stats.TotalCodOrderNum > 0 {
				countryParcelCostVolume[strconv.Itoa(int(productID))+"Cod"] += 1
			}
			if stats.TotalBulkyOrderNum > 0 {
				countryParcelCostVolume[strconv.Itoa(int(productID))+"Bulky"] += 1
			}
			if stats.TotalHighValueOrderNum > 0 {
				countryParcelCostVolume[strconv.Itoa(int(productID))+"HighValue"] += 1
			}
			if stats.TotalDgOrderNum > 0 {
				countryParcelCostVolume[strconv.Itoa(int(productID))+"Dg"] += 1
			}
		}
		if orderResult.ShippingFee != batch_allocate.IllegalEsf {
			allocateSfMap[productID] += orderResult.ShippingFee
		}
	}
	// product-name map
	productNameMap := localcache.AllItems(ctx, constant.ProductNameDict)
	distributions := make([]allocation3.Distribution, 0)
	for product, costVolume := range countryCostVolume {
		volumeInfo, exist := b.logInfo.CountryVolumeMap[product]
		if !exist {
			logger.CtxLogErrorf(ctx, "product not in country volume map: %d", product)
			continue
		}

		productNameData := productNameMap[strconv.FormatInt(product, 10)]
		productName, _ := productNameData.(string)
		distributions = append(distributions, allocation3.Distribution{
			InputProduct:                  strconv.FormatInt(product, 10),
			ProductName:                   productName,
			AllocationShippingFee:         strconv.FormatFloat(allocateSfMap[product], 'f', -1, 64),
			MaxDailyLimitCountry:          volumeInfo.MaxDailyLimit,
			MaxDailyCodLimitCountry:       volumeInfo.MaxCodDailyLimit,
			MaxDailyBulkyLimitCountry:     volumeInfo.MaxBulkyDailyLimit,
			MaxDailyHighValueLimitCountry: volumeInfo.MaxHighValueDailyLimit,
			MaxDailyDgLimitCountry:        volumeInfo.MaxDgDailyLimit,
			MinBatchLimitCountry:          volumeInfo.MinBatchLimit,
			SystemVolumeProduct:           volumeInfo.SystemVolume + costVolume,
			SystemVolumeCod:               volumeInfo.SystemCodVolume + countryParcelCostVolume[strconv.Itoa(int(product))+"Cod"],
			SystemVolumeBulky:             volumeInfo.SystemBulkyVolume + countryParcelCostVolume[strconv.Itoa(int(product))+"Bulky"],
			SystemVolumeHighValue:         volumeInfo.SystemHighValueVolume + countryParcelCostVolume[strconv.Itoa(int(product))+"HighValue"],
			SystemVolumeDg:                volumeInfo.SystemDgVolume + countryParcelCostVolume[strconv.Itoa(int(product))+"Dg"],
		})
	}
	return distributions
}

func (b *BatchAllocateServiceImpl) copyOrderInfoMap(ctx context.Context, orderInfoMap map[uint64]*allocation3.AllocateOrderInfoForLog) map[uint64]*allocation3.AllocateOrderInfoForLog {
	newOrderInfoMap := make(map[uint64]*allocation3.AllocateOrderInfoForLog, 0)
	for orderID, info := range orderInfoMap {
		orderInfo := &allocation3.AllocateOrderInfoForLog{
			OrderID:                orderID,
			ZoneCodeList:           info.ZoneCodeList,
			RouteCodeList:          info.RouteCodeList,
			Inputs:                 info.Inputs,
			PickupEffWhitelistInfo: info.PickupEffWhitelistInfo,
		}
		zoneVolumeMap := make(map[int64]map[string]*allocation3.VolumeInfo, 0)
		routeVolumeMap := make(map[int64]map[string]*allocation3.VolumeInfo, 0)
		for _, product := range info.Inputs {
			if zoneVolumeMap[int64(product)] == nil {
				zoneVolumeMap[int64(product)] = make(map[string]*allocation3.VolumeInfo, 0)
			}
			if routeVolumeMap[int64(product)] == nil {
				routeVolumeMap[int64(product)] = make(map[string]*allocation3.VolumeInfo, 0)
			}
			for _, zoneCode := range info.ZoneCodeList {
				zoneVolumeMap[int64(product)][zoneCode] = b.logInfo.ZoneVolumeMap[int64(product)][zoneCode]
			}
			for _, routeCode := range info.RouteCodeList {
				routeVolumeMap[int64(product)][routeCode] = b.logInfo.RouteVolumeMap[int64(product)][routeCode]
			}
		}
		orderInfo.ZoneVolumeMap = zoneVolumeMap
		orderInfo.RouteVolumeMap = routeVolumeMap
		newOrderInfoMap[orderID] = orderInfo
	}
	return newOrderInfoMap
}
