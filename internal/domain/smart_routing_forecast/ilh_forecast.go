package smart_routing_forecast

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"

	entity2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient/lfsentity"
	availableLHEntity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

func (s *SmartRoutingForecastServiceImpl) ReadILHForecastOrder(
	ctx context.Context, task *persistent.ForecastingTaskTab, day time.Time,
) ([]*forecastentity.LHSOrderInfo, *srerr.Error) {
	if task.ShipmentResource == persistent.ShipmentResourceTypeHistoryOrders {
		return s.readProductILHForecastOrdersFromHistoryOrders(ctx, task, day)
	}

	return s.readILHForecastOrdersFromPredictionVolumes(ctx, task, day)
}

func (s *SmartRoutingForecastServiceImpl) readProductILHForecastOrdersFromHistoryOrders(
	ctx context.Context, task *persistent.ForecastingTaskTab, day time.Time,
) ([]*forecastentity.LHSOrderInfo, *srerr.Error) {
	orders, err := s.OrderSyncRepo.ReadILHForecastOrderIteration(ctx, task.ProductId, day)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ReadForecastOrderIteration fail|err=%v", err)
		return nil, err
	}

	sortingCodeResp, err := s.LfsApi.ListSortingCode(ctx, &lfsentity.ListSortingCodeRequest{ProductId: strconv.Itoa(task.ProductId)})
	if err != nil {
		return nil, err
	}

	sortingCodeToInfoMap := make(map[string]*lfsentity.SortingCodeInfo)
	keyToSortingCodeInfoMap := make(map[string]*lfsentity.SortingCodeInfo)
	for _, s := range sortingCodeResp.Data.List {
		sortingCodeToInfoMap[s.ServiceCode] = s
		if s.CodeStatus == lfsentity.CodeStatusEnable {
			keyToSortingCodeInfoMap[formatSortingCodeKey(s.DgGroup, s.LmId, s.DgFlag, s.ActualPointId)] = s
		}
	}

	lineToDgGroupMap, err := s.loadDgGroupMapByProduct(ctx, task.ProductId)
	if err != nil {
		return nil, err
	}

	if err := s.batchLoadOrderILHLanes(ctx, orders, sortingCodeToInfoMap, lineToDgGroupMap); err != nil {
		logger.CtxLogErrorf(ctx, "batchLoadOrderILHLanes failed | err: %v", err)
		return nil, err
	}

	return orders, nil
}

func (s *SmartRoutingForecastServiceImpl) readILHForecastOrdersFromHistoryOrders(
	ctx context.Context, day time.Time,
) ([]*forecastentity.LHSOrderInfo, *srerr.Error) {
	orders, err := s.OrderSyncRepo.ReadILHForecastOrderIteration(ctx, 0, day)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ReadForecastOrderIteration fail|err=%v", err)
		return nil, err
	}

	sortingCodeResp, err := s.LfsApi.ListSortingCode(ctx, &lfsentity.ListSortingCodeRequest{Region: envvar.GetCID()})
	if err != nil {
		return nil, err
	}

	sortingCodeToInfoMap := make(map[string]*lfsentity.SortingCodeInfo)
	for _, s := range sortingCodeResp.Data.List {
		sortingCodeToInfoMap[s.ServiceCode] = s
	}

	lineToDgGroupMap := make(map[string]string)
	if err := s.batchLoadOrderILHLanes(ctx, orders, sortingCodeToInfoMap, lineToDgGroupMap); err != nil {
		logger.CtxLogErrorf(ctx, "batchLoadOrderILHLanes failed | err: %v", err)
		return nil, err
	}

	return orders, nil
}

func (s *SmartRoutingForecastServiceImpl) readILHForecastOrdersFromPredictionVolumes(
	ctx context.Context, task *persistent.ForecastingTaskTab, day time.Time,
) ([]*forecastentity.LHSOrderInfo, *srerr.Error) {
	condition := map[string]interface{}{
		"task_id = ?":       task.Id,
		"shipment_date = ?": day.Format(timeutil.DateFormat),
	}
	predictionVolumes, err := s.ForecastRepo.GetPredictionVolumesByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	produceDetail, err := s.LpsApi.GetProductDetail(ctx, task.ProductId)
	if err != nil {
		return nil, err
	}

	var allIlhLanes []*rule.RoutingLaneInfo
	for _, laneCode := range produceDetail.IlhLaneCodes {
		routingLaneInfo, err := s.RoutingSrv.LoadRoutingLaneInfoByLaneCode(ctx, laneCode)
		if err != nil {
			return nil, err
		}
		allIlhLanes = append(allIlhLanes, routingLaneInfo)
	}

	var orders []*forecastentity.LHSOrderInfo
	for _, p := range predictionVolumes {
		var availableLanes []*rule.RoutingLaneInfo
		for _, l := range allIlhLanes {
			if l.DestinationPort == p.DestSite {
				availableLanes = append(availableLanes, l)
			}
		}

		for i := 0; i < p.CartonQuantity; i++ {
			var (
				// KG to G
				weight    = int(p.TotalWeight * 1000 / float64(p.CartonQuantity))
				parcelQty = int(float64(p.ParcelQuantity) / float64(p.CartonQuantity))
			)

			orders = append(orders, &forecastentity.LHSOrderInfo{
				LhsTn:          strconv.Itoa(int(p.Id)),
				MultiProductId: p.MultiProductId,
				DgType:         p.TP,
				TwsCode:        p.Tws,
				Ctime:          int(day.Unix()),
				ActualWeight:   weight,
				ParcelQuantity: parcelQty,
				LmId:           p.Lm,
				AvailableLanes: availableLanes,
			})
		}
	}

	return orders, nil
}

func (s *SmartRoutingForecastServiceImpl) readRevampILHForecastOrdersFromPredictionVolumes(
	ctx context.Context, taskID int, day time.Time,
) ([]*forecastentity.LHSOrderInfo, *srerr.Error) {
	condition := map[string]interface{}{
		"task_id = ?":       taskID,
		"shipment_date = ?": day.Format(timeutil.DateFormat),
	}
	predictionVolumes, err := s.ForecastRepo.GetPredictionVolumesByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	productAllAvailableLanesMap := make(map[int][]*rule.RoutingLaneInfo)
	for _, p := range predictionVolumes {
		if _, exist := productAllAvailableLanesMap[p.MultiProductId]; exist {
			continue
		}

		produceDetail, err := s.LpsApi.GetProductDetail(ctx, p.MultiProductId)
		if err != nil {
			return nil, err
		}

		var allIlhLanes []*rule.RoutingLaneInfo
		for _, laneCode := range produceDetail.IlhLaneCodes {
			routingLaneInfo, err := s.RoutingSrv.LoadRoutingLaneInfoByLaneCode(ctx, laneCode)
			if err != nil {
				return nil, err
			}
			allIlhLanes = append(allIlhLanes, routingLaneInfo)
		}
		productAllAvailableLanesMap[p.MultiProductId] = allIlhLanes
	}

	var orders []*forecastentity.LHSOrderInfo
	for _, p := range predictionVolumes {
		if p.MultiProductId == 0 {
			continue
		}

		var availableLanes []*rule.RoutingLaneInfo
		for _, l := range productAllAvailableLanesMap[p.MultiProductId] {
			if l.DestinationPort == p.DestSite {
				availableLanes = append(availableLanes, l)
			}
		}

		for i := 0; i < p.CartonQuantity; i++ {
			var (
				// KG to G
				weight    = int(p.TotalWeight * 1000 / float64(p.CartonQuantity))
				parcelQty = int(float64(p.ParcelQuantity) / float64(p.CartonQuantity))
			)

			orders = append(orders, &forecastentity.LHSOrderInfo{
				LhsTn:          strconv.Itoa(int(p.Id)),
				MultiProductId: p.MultiProductId,
				DgType:         p.TP,
				TwsCode:        p.Tws,
				Ctime:          int(day.Unix()),
				ActualWeight:   weight,
				ParcelQuantity: parcelQty,
				LmId:           p.Lm,
				AvailableLanes: availableLanes,
			})
		}
	}

	return orders, nil
}

func (s *SmartRoutingForecastServiceImpl) ReadILHAllForecastOrder(
	ctx context.Context, taskID int, shipmentResource persistent.ShipmentResource, day time.Time,
) ([]*forecastentity.LHSOrderInfo, *srerr.Error) {
	if shipmentResource == persistent.ShipmentResourceTypeHistoryOrders {
		return s.readILHForecastOrdersFromHistoryOrders(ctx, day)
	}

	return s.readRevampILHForecastOrdersFromPredictionVolumes(ctx, taskID, day)
}

func (s *SmartRoutingForecastServiceImpl) StartILHForecast(ctx context.Context, task ILHForecastTask) *srerr.Error {
	days, daysErr := getDays(task.StartDate, task.EndDate)
	if daysErr != nil {
		logger.CtxLogErrorf(ctx, "get days failed | err=%v", daysErr)
		return srerr.With(srerr.DataErr, task, daysErr)
	}

	// 准备预测所需的映射数据
	availableLHRulesMap := s.prepareAvailableLHRulesMap(task.AvailableLHRuleList)
	lhCapacityMap := s.prepareLHCapacityMap(task.LHCapacityList)

	// 用于统计预测结果
	resultMap := make(map[string]*forecastentity.ILHForecastResult)

	// 对每一天进行预测
	if err := s.processDailyForecasts(ctx, task, days, availableLHRulesMap, lhCapacityMap, resultMap); err != nil {
		return err
	}

	// 如果结果为空，直接返回错误
	if len(resultMap) == 0 {
		logger.CtxLogErrorf(ctx, "No forecast results generated, skipping saving results")
		return srerr.New(srerr.ForecastFailed, nil, "No forecast results generated, skipping saving results")
	}

	// 将聚合统计的结果保存到数据库
	if err := s.saveILHForecastResults(ctx, resultMap); err != nil {
		logger.CtxLogErrorf(ctx, "Failed to save ILH forecast results | err=%+v", err)
		return err
	}

	return nil
}

// prepareAvailableLHRulesMap 按照MultiProductID预处理AvailableLHRule
func (s *SmartRoutingForecastServiceImpl) prepareAvailableLHRulesMap(ruleList []forecastentity.ForecastAvailableLHRule) map[int][]availableLHEntity.AvailableLHRule {
	availableLHRulesMap := make(map[int][]availableLHEntity.AvailableLHRule)
	for _, forecastRule := range ruleList {
		availableLHRulesMap[forecastRule.MultiProductID] = forecastRule.Rules
	}
	return availableLHRulesMap
}

// prepareLHCapacityMap 预处理LH容量信息
func (s *SmartRoutingForecastServiceImpl) prepareLHCapacityMap(capacityList []forecastentity.ForecastLHCapacityItem) map[string][]forecastentity.ForecastLHCapacityItem {
	lhCapacityMap := make(map[string][]forecastentity.ForecastLHCapacityItem)
	for _, forecastRule := range capacityList {
		lhCapacityMap[forecastRule.ILHLineID] = append(lhCapacityMap[forecastRule.ILHLineID], forecastRule)
	}
	return lhCapacityMap
}

// processDailyForecasts 处理每一天的预测任务
func (s *SmartRoutingForecastServiceImpl) processDailyForecasts(
	ctx context.Context,
	task ILHForecastTask,
	days []time.Time,
	availableLHRulesMap map[int][]availableLHEntity.AvailableLHRule,
	lhCapacityMap map[string][]forecastentity.ForecastLHCapacityItem,
	resultMap map[string]*forecastentity.ILHForecastResult,
) *srerr.Error {

	// 不同订单复用一个Counter，Counter会根据实际订单日期进行区分
	forecastILHWeightCounter := volume_counter.NewForecastILHWeightCounterImpl()
	ilhRoutingService := routing.NewILHRoutingServiceImpl(forecastILHWeightCounter)
	originalRequestID := requestid.GetFromCtx(ctx)

	for _, day := range days {
		orderInfos, err := s.ReadILHAllForecastOrder(ctx, task.ID, task.ShipmentResource, day)
		if err != nil {
			logger.CtxLogErrorf(ctx, "ReadILHAllForecastOrder failed | err=%+v", err)
			return err
		}

		for _, orderInfo := range orderInfos {
			// 为每个订单创建新的上下文，在原request ID基础上拼接lhs tn
			newRequestID := originalRequestID + "|" + orderInfo.LhsTn
			orderCtx := requestid.SetToCtx(ctx, newRequestID)
			orderCtx = logger.NewLogContext(orderCtx, newRequestID)

			if err := s.processOrderForForecast(
				orderCtx, task, orderInfo, availableLHRulesMap, lhCapacityMap, resultMap, ilhRoutingService, forecastILHWeightCounter,
			); err != nil {
				logger.CtxLogErrorf(orderCtx, "processOrderForForecast failed for order %s | err=%+v", orderInfo.LhsTn, err)
				// 允许单个订单失败，继续处理其他订单
			}
		}
	}

	logger.CtxLogInfof(ctx, "Successfully processed all orders for ILH forecast, Counter: %s",
		str.JsonString(forecastILHWeightCounter.GetCounter()))

	return nil
}

// processOrderForForecast 处理单个订单的预测路由
func (s *SmartRoutingForecastServiceImpl) processOrderForForecast(
	ctx context.Context,
	task ILHForecastTask,
	orderInfo *forecastentity.LHSOrderInfo,
	availableLHRulesMap map[int][]availableLHEntity.AvailableLHRule,
	lhCapacityMap map[string][]forecastentity.ForecastLHCapacityItem,
	resultMap map[string]*forecastentity.ILHForecastResult,
	ilhRoutingService routing.ILHRoutingService,
	counter *volume_counter.ForecastILHWeightCounterImpl,
) *srerr.Error {

	counter.SetOrderTime(int64(orderInfo.Ctime))
	destinationPorts := s.extractDestinationPorts(orderInfo.AvailableLanes)

	availableLHRule, existAvailableLHRule := s.findMatchingAvailableLHRule(
		ctx,
		orderInfo.MultiProductId,
		orderInfo.DgType,
		orderInfo.TwsCode,
		destinationPorts,
		availableLHRulesMap,
	)

	if !existAvailableLHRule {
		return nil
	}

	ilhLines := routing.ExtractResourceLineIDs(orderInfo.AvailableLanes, lfslib.ILHLine)
	ilhCapacitySettingMap := s.getILHCapacitySettings(ctx, ilhLines, orderInfo.TwsCode, orderInfo.DgType, destinationPorts, lhCapacityMap)

	// 创建RevampILHRoutingRequest请求
	routingRequest := routing.RevampILHRoutingRequest{
		ProductID:             orderInfo.MultiProductId,
		LMID:                  orderInfo.LmId,
		DGType:                orderInfo.DgType,
		TWSCode:               orderInfo.TwsCode,
		OrderWeight:           int64(orderInfo.ActualWeight),
		OrderTime:             int64(orderInfo.Ctime),
		AvailableLanes:        orderInfo.AvailableLanes,
		AvailableLHRule:       availableLHRule,
		ILHCapacitySettingMap: ilhCapacitySettingMap,
	}

	result, err := ilhRoutingService.RevampILHRouting(ctx, routingRequest)
	if err != nil {
		logger.CtxLogErrorf(ctx, "RevampILHRouting failed for order %s | err=%+v", orderInfo.LhsTn, err)
		s.collectForecastResult(task.ID, availableLHRule, orderInfo, nil, resultMap)

		return err
	}

	logger.CtxLogInfof(ctx, "Successfully processed routing for order %s", orderInfo.LhsTn)

	// 对预测结果进行统计
	if result != nil && result.Lane != nil {
		// 从Lane的LineList中提取ILH和CC的LineID
		var ilhLineID, ccLineID string
		for _, lineInfo := range result.Lane.LineList {
			if objutil.ContainInt(lfslib.ILHLine, int(lineInfo.ResourceSubType)) {
				ilhLineID = lineInfo.LineId
			} else if objutil.ContainInt(lfslib.CCLine, int(lineInfo.ResourceSubType)) {
				ccLineID = lineInfo.LineId
			}
		}

		totalBSAUsage := result.ReservedBSAUsage + result.NonReservedBSAUsage
		destPortID := result.Lane.DestinationPort

		_ = counter.IncrILHBSAWeight(ctx, ilhLineID, orderInfo.DgType, orderInfo.TwsCode, destPortID, result.SlotID, totalBSAUsage)
		_ = counter.IncrILHProductBSAWeight(ctx, orderInfo.MultiProductId, ilhLineID, orderInfo.DgType, orderInfo.TwsCode, destPortID, result.SlotID, totalBSAUsage)
		_ = counter.IncrILHAdhocWeight(ctx, ilhLineID, orderInfo.DgType, orderInfo.TwsCode, destPortID, result.SlotID, result.AdhocUsage)
		_ = counter.IncrCCWeight(ctx, orderInfo.MultiProductId, ccLineID, int64(orderInfo.ActualWeight))

		for slotID, basUsage := range result.InheritedBSASlotUsage {
			_ = counter.IncrILHBSAWeight(ctx, ilhLineID, orderInfo.DgType, orderInfo.TwsCode, destPortID, slotID, basUsage)
		}

		for slotID, adhocUsage := range result.InheritedAdhocSlotUsage {
			_ = counter.IncrILHAdhocWeight(ctx, ilhLineID, orderInfo.DgType, orderInfo.TwsCode, destPortID, slotID, adhocUsage)
		}

		s.collectForecastResult(task.ID, availableLHRule, orderInfo, result, resultMap)
	}

	return nil
}

// extractDestinationPorts 提取可用航线的目的地端口
func (s *SmartRoutingForecastServiceImpl) extractDestinationPorts(availableLanes []*rule.RoutingLaneInfo) []string {
	destinationPorts := make([]string, 0, len(availableLanes))
	for _, routingLaneInfo := range availableLanes {
		destinationPorts = append(destinationPorts, routingLaneInfo.DestinationPort)
	}
	return objutil.RemoveDuplicateString(destinationPorts)
}

// findMatchingAvailableLHRule 查找匹配的可用LH规则
func (s *SmartRoutingForecastServiceImpl) findMatchingAvailableLHRule(
	ctx context.Context,
	multiProductId int,
	dgType int,
	twsCode string,
	destinationPorts []string,
	availableLHRulesMap map[int][]availableLHEntity.AvailableLHRule,
) (availableLHEntity.AvailableLHRule, bool) {
	for _, a := range availableLHRulesMap[multiProductId] {
		if a.DGType != rule.UndefinedDGFlag && a.DGType != rule.DGFlag(dgType) {
			continue
		}
		if len(a.TWS) > 0 && !objutil.ContainStr(a.TWS, twsCode) {
			continue
		}
		if len(a.DestinationPort) > 0 && !objutil.HaveIntersection(a.DestinationPort, destinationPorts) {
			continue
		}
		return a, true
	}

	// 如果找不到Forecast Available LH Rule，则尝试匹配线上的
	logger.CtxLogInfof(ctx, "get forecast available lh rule failed, try to match live rule, multiProductId=%d, dgType=%d, twsCode=%s, destinationPorts=%v",
		multiProductId, dgType, twsCode, destinationPorts)

	availableLHRule, err := s.AvailableLHService.GetAvailableLHRule(ctx, multiProductId, dgType, twsCode, destinationPorts)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get live available lh rule failed, multiProductId=%d, dgType=%d, twsCode=%s, destinationPorts=%v, err=%v",
			multiProductId, dgType, twsCode, destinationPorts, err)
		return availableLHEntity.AvailableLHRule{}, false
	}

	return availableLHRule, true
}

// collectForecastResult 收集预测结果
func (s *SmartRoutingForecastServiceImpl) collectForecastResult(
	taskID int,
	availableLHRule availableLHEntity.AvailableLHRule,
	orderInfo *forecastentity.LHSOrderInfo,
	result *routing.RevampILHRoutingResult,
	resultMap map[string]*forecastentity.ILHForecastResult,
) {
	var (
		laneCode             string
		destPort             string
		reservedBSAWeight    float64
		nonReversedBSAWeight float64
		adhocWeight          float64
	)

	// 检查结果是否为失败情况
	if result == nil || result.Lane == nil {
		// 处理路由失败情况
		laneCode = forecast.Blocked
		destPort = ""
		reservedBSAWeight = 0
		nonReversedBSAWeight = 0
		adhocWeight = float64(orderInfo.ActualWeight) // 将订单重量记录在AdhocWeight字段
	} else {
		// 处理成功情况
		laneCode = result.Lane.LaneCode
		destPort = result.Lane.DestinationPort
		reservedBSAWeight = float64(result.ReservedBSAUsage)
		nonReversedBSAWeight = float64(result.NonReservedBSAUsage)
		adhocWeight = float64(result.AdhocUsage)
	}

	// 生成结果统计的唯一键
	key := generateResultKey(taskID, availableLHRule.RuleName, availableLHRule.Priority,
		orderInfo.MultiProductId, laneCode, destPort, rule.DGFlag(orderInfo.DgType))

	// 将结果聚合到领域对象map中
	if _, exists := resultMap[key]; !exists {
		resultMap[key] = &forecastentity.ILHForecastResult{
			TaskID:                  taskID,
			AvailableLHRuleName:     availableLHRule.RuleName,
			AvailableLHRulePriority: availableLHRule.Priority,
			MultiProductID:          orderInfo.MultiProductId,
			LaneCode:                laneCode,
			DestinationPort:         destPort,
			DGType:                  rule.DGFlag(orderInfo.DgType),
			ReversedBSAWeight:       0,
			NonReversedBSAWeight:    0,
			AdhocWeight:             0,
		}
	}

	// 累加容量使用量
	resultMap[key].ReversedBSAWeight += reservedBSAWeight
	resultMap[key].NonReversedBSAWeight += nonReversedBSAWeight
	resultMap[key].AdhocWeight += adhocWeight
}

// generateResultKey 生成用于统计结果的唯一键
func generateResultKey(taskID int, ruleName string, rulePriority int, productID int, laneCode string, destPort string, dgType rule.DGFlag) string {
	return fmt.Sprintf("%d-%s-%d-%d-%s-%s-%d",
		taskID, ruleName, rulePriority, productID, laneCode, destPort, dgType)
}

// saveILHForecastResults 保存ILH预测统计结果
func (s *SmartRoutingForecastServiceImpl) saveILHForecastResults(ctx context.Context, results map[string]*forecastentity.ILHForecastResult) *srerr.Error {
	// 直接创建结果切片
	resultSlice := make([]*forecastentity.ILHForecastResult, 0, len(results))

	// 将map中的结果添加到切片中
	for _, result := range results {
		resultSlice = append(resultSlice, result)
	}

	// 批量保存结果到数据库
	if len(resultSlice) > 0 {
		// 通过ILHForecastTaskService保存结果
		return s.ILHForecastTaskService.BatchSaveResults(ctx, resultSlice)
	}

	return nil
}

func (s *SmartRoutingForecastServiceImpl) getILHCapacitySettings(
	ctx context.Context, ilhLines []string, twsCode string, dgType int, destinationPorts []string,
	lhCapacityMap map[string][]forecastentity.ForecastLHCapacityItem,
) map[string]entity2.ILHCapacitySettingInfo {

	ret := make(map[string]entity2.ILHCapacitySettingInfo)
	for _, ilhLineID := range ilhLines {
		ilhCapacityList := lhCapacityMap[ilhLineID]
		for _, ilhCapacity := range ilhCapacityList {
			if ilhCapacity.DGType != rule.UndefinedDGFlag && ilhCapacity.DGType != rule.DGFlag(dgType) {
				continue
			}
			if len(ilhCapacity.TWS) > 0 && !objutil.ContainStr(ilhCapacity.TWS, twsCode) {
				continue
			}
			if len(ilhCapacity.DestinationPort) > 0 && !objutil.HaveIntersection(ilhCapacity.DestinationPort, destinationPorts) {
				continue
			}

			capacitySetting, exist := entity2.GetHighestPriorityCapacitySetting(ctx, ilhCapacity.CapacitySettings, twsCode)
			if !exist {
				logger.CtxLogInfof(ctx, "No capacity settings found for ilh=%s", ilhLineID)
				continue
			}

			ret[ilhLineID] = entity2.ILHCapacitySettingInfo{
				CapacitySetting:             capacitySetting,
				TWS:                         ilhCapacity.TWS,
				DestPorts:                   ilhCapacity.DestinationPort,
				InheritanceTimeSlotCapacity: entity2.GetInheritanceTimeSlotCapacity(ctx, ilhCapacity.CapacitySettings, twsCode),
			}
		}
	}

	// 对找不到Forecast CapacitySetting的ILH尝试匹配线上的
	for _, ilhLineID := range ilhLines {
		if _, exist := ret[ilhLineID]; exist {
			continue
		}

		logger.CtxLogInfof(ctx, "get forecast capacity setting failed, try to match live rule, ilhLineID=%s, twsCode=%s, dgType=%d, destinationPorts=%v",
			ilhLineID, twsCode, dgType, destinationPorts)

		capacitySettingInfo, err := s.LHCapacityService.GetLHCapacitySettingInfo(ctx, ilhLineID, dgType, twsCode, destinationPorts)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get live capacity setting failed, ilhLineID=%s, twsCode=%s, dgType=%d, destinationPorts=%v, err=%v",
				ilhLineID, twsCode, dgType, destinationPorts, err)
			continue
		}

		ret[ilhLineID] = capacitySettingInfo
	}

	return ret
}
