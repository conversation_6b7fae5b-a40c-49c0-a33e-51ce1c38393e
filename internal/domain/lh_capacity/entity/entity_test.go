package entity

import (
	"testing"
	"time"
)

func Test_processSpecialTimeSlot(t *testing.T) {
	tests := []struct {
		name        string
		setting     CapacitySetting
		currentTime int64
		cutoffTime  int64
		loc         *time.Location
		wantSlotID  string
		wantValid   bool
	}{
		{
			name: "当前时间在今天cutoff之后，时间段有效",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialTimeSlotStartTime: "13:00:00",
					SpecialTimeSlotEndTime:   "15:00:00",
				},
			},
			// 当前时间为16:00，在今天的cutoff(12:00)之后
			currentTime: time.Date(2023, 1, 1, 16, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			wantSlotID: "1300-1500",
			wantValid:  true,
		},
		{
			name: "当前时间在今天cutoff之前，但跨越参考时间点",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					// 设置时间段在前一天的cutoff(12:00)之后到第二天(今天)的当前时间之前
					SpecialTimeSlotStartTime: "15:00:00", // 昨天15:00
					SpecialTimeSlotEndTime:   "20:00:00", // 昨天20:00，未到今天11:00
				},
			},
			// 当前时间为11:00，在今天的cutoff(12:00)之前
			currentTime: time.Date(2023, 1, 1, 11, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			// 在修改后的逻辑中，昨天的15:00-20:00这个时间段在昨天的cutoff之后，今天的当前时间之前，应该匹配成功
			wantSlotID: "1500-2000",
			wantValid:  true,
		},
		{
			name: "时间段开始时间早于参考cutoff时间，匹配失败",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialTimeSlotStartTime: "10:00:00", // 开始时间早于cutoff
					SpecialTimeSlotEndTime:   "14:00:00",
				},
			},
			// 当前时间为15:00，在今天的cutoff(12:00)之后
			currentTime: time.Date(2023, 1, 1, 15, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			wantSlotID: "",
			wantValid:  false, // 开始时间早于cutoff，应该匹配失败
		},
		{
			name: "时间段结束时间晚于当前时间，匹配失败",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialTimeSlotStartTime: "13:00:00",
					SpecialTimeSlotEndTime:   "16:00:00", // 结束时间晚于当前时间
				},
			},
			// 当前时间为15:00，在今天的cutoff(12:00)之后
			currentTime: time.Date(2023, 1, 1, 15, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			wantSlotID: "",
			wantValid:  false, // 结束时间晚于当前时间，应该匹配失败
		},
		{
			name: "结束时间刚好等于当前时间，匹配成功",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialTimeSlotStartTime: "12:30:00",
					SpecialTimeSlotEndTime:   "15:00:00",
				},
			},
			// 当前时间为15:00，刚好等于结束时间
			currentTime: time.Date(2023, 1, 1, 15, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			wantSlotID: "1230-1500",
			wantValid:  true, // 结束时间等于当前时间，应该匹配成功
		},
		{
			name: "开始时间刚好等于cutoff时间，匹配成功",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialTimeSlotStartTime: "12:00:00", // 开始时间等于cutoff
					SpecialTimeSlotEndTime:   "14:00:00",
				},
			},
			// 当前时间为15:00，在cutoff(12:00)之后
			currentTime: time.Date(2023, 1, 1, 15, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			wantSlotID: "1200-1400",
			wantValid:  true, // 开始时间等于cutoff，应该匹配成功
		},
		{
			name: "时间格式错误",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialTimeSlotStartTime: "11:00:00",
					SpecialTimeSlotEndTime:   "不正确的时间格式",
				},
			},
			currentTime: time.Date(2023, 1, 1, 13, 0, 0, 0, time.UTC).Unix(),
			cutoffTime:  time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:         time.UTC,
			wantSlotID:  "",
			wantValid:   false,
		},
		{
			name: "不同时区测试 - 东八区(+8)",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialTimeSlotStartTime: "13:00:00", // 北京时间13:00
					SpecialTimeSlotEndTime:   "15:00:00", // 北京时间15:00
				},
			},
			// 当前时间为北京时间16:00，UTC 8:00
			currentTime: time.Date(2023, 1, 1, 8, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为北京时间12:00，UTC 4:00
			cutoffTime: time.Date(2023, 1, 1, 4, 0, 0, 0, time.UTC).Unix(),
			loc:        time.FixedZone("Asia/Shanghai", 8*60*60),
			wantSlotID: "1300-1500",
			wantValid:  true, // 在修改后的逻辑中，这个时间段在cutoff之后，当前时间之前，应该匹配成功
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotSlotID, gotValid := processSpecialTimeSlot(tt.setting, tt.currentTime, tt.cutoffTime, tt.loc)
			if gotSlotID != tt.wantSlotID {
				t.Errorf("processSpecialTimeSlot() gotSlotID = %v, want %v", gotSlotID, tt.wantSlotID)
			}
			if gotValid != tt.wantValid {
				t.Errorf("processSpecialTimeSlot() gotValid = %v, want %v", gotValid, tt.wantValid)
			}
		})
	}
}

func Test_processSpecialDateAndTimeSlot(t *testing.T) {
	// 创建一个东八区时区
	cstZone := time.FixedZone("Asia/Shanghai", 8*60*60)

	tests := []struct {
		name        string
		setting     CapacitySetting
		currentTime int64
		cutoffTime  int64
		loc         *time.Location
		wantSlotID  string
		wantValid   bool
	}{
		{
			name: "当前时间在今天cutoff之后，时间段结束时间早于当前时间，匹配成功",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialDateAndTimeSlotStartTime: "2023-01-01 13:00:00",
					SpecialDateAndTimeSlotEndTime:   "2023-01-01 15:00:00",
				},
			},
			// 当前时间为16:00，在今天的cutoff(12:00)之后
			currentTime: time.Date(2023, 1, 1, 16, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			wantSlotID: "1300-1500",
			wantValid:  true,
		},
		{
			name: "当前时间在今天cutoff之前，昨天日期的时间段测试，匹配成功",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					// 这里关键是使用有效的日期
					SpecialDateAndTimeSlotStartTime: "2022-12-31 15:00:00", // 昨天15:00
					SpecialDateAndTimeSlotEndTime:   "2022-12-31 20:00:00", // 昨天20:00，在今天11:00之前结束
				},
			},
			// 当前时间为11:00，在今天的cutoff(12:00)之前
			currentTime: time.Date(2023, 1, 1, 11, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			wantSlotID: "1500-2000",
			wantValid:  true, // 时间段在昨天的cutoff之后，今天的当前时间之前，应该匹配成功
		},
		{
			name: "开始时间早于参考cutoff时间，匹配失败",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialDateAndTimeSlotStartTime: "2023-01-01 10:00:00", // 开始时间早于cutoff
					SpecialDateAndTimeSlotEndTime:   "2023-01-01 14:00:00",
				},
			},
			// 当前时间为15:00，在今天的cutoff(12:00)之后
			currentTime: time.Date(2023, 1, 1, 15, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			wantSlotID: "",
			wantValid:  false, // 开始时间早于cutoff时间，应该匹配失败
		},
		{
			name: "结束时间晚于当前时间，匹配失败",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialDateAndTimeSlotStartTime: "2023-01-01 13:00:00",
					SpecialDateAndTimeSlotEndTime:   "2023-01-01 16:00:00", // 结束时间晚于当前时间
				},
			},
			// 当前时间为15:00，在今天的cutoff(12:00)之后
			currentTime: time.Date(2023, 1, 1, 15, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			wantSlotID: "",
			wantValid:  false, // 结束时间晚于当前时间，应该匹配失败
		},
		{
			name: "时间格式错误",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialDateAndTimeSlotStartTime: "错误的格式",
					SpecialDateAndTimeSlotEndTime:   "2023-01-01 14:00:00",
				},
			},
			currentTime: time.Date(2023, 1, 1, 13, 0, 0, 0, time.UTC).Unix(),
			cutoffTime:  time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:         time.UTC,
			wantSlotID:  "",
			wantValid:   false,
		},
		{
			name: "不同时区测试 - 东八区(+8)",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialDateAndTimeSlotStartTime: "2023-01-01 13:00:00", // 北京时间13:00
					SpecialDateAndTimeSlotEndTime:   "2023-01-01 15:00:00", // 北京时间15:00，早于当前时间16:00
				},
			},
			// 当前时间为北京时间16:00，UTC 8:00
			currentTime: time.Date(2023, 1, 1, 8, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为北京时间12:00，UTC 4:00
			cutoffTime: time.Date(2023, 1, 1, 4, 0, 0, 0, time.UTC).Unix(),
			// 使用北京时区
			loc:        cstZone,
			wantSlotID: "1300-1500",
			wantValid:  true,
		},
		{
			name: "跨日期测试，结束时间晚于当前时间，匹配失败",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialDateAndTimeSlotStartTime: "2023-01-01 20:00:00",
					SpecialDateAndTimeSlotEndTime:   "2023-01-02 02:00:00", // 结束时间晚于当前时间
				},
			},
			// 当前时间为1月1日22:00，在cutoff(12:00)之后
			currentTime: time.Date(2023, 1, 1, 22, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为1月1日12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			wantSlotID: "",
			wantValid:  false, // 结束时间晚于当前时间，应该匹配失败
		},
		{
			name: "开始时间晚于当前时间，匹配失败",
			setting: CapacitySetting{
				TimeObject: TimeObject{
					SpecialDateAndTimeSlotStartTime: "2023-01-02 13:00:00", // 开始时间晚于当前时间
					SpecialDateAndTimeSlotEndTime:   "2023-01-02 15:00:00",
				},
			},
			// 当前时间为1月1日14:00，在今天的cutoff(12:00)之后
			currentTime: time.Date(2023, 1, 1, 14, 0, 0, 0, time.UTC).Unix(),
			// cutoff时间为1月1日12:00
			cutoffTime: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC).Unix(),
			loc:        time.UTC,
			wantSlotID: "",
			wantValid:  false, // 开始时间晚于当前时间，应该匹配失败
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotSlotID, gotValid := processSpecialDateAndTimeSlot(tt.setting, tt.currentTime, tt.cutoffTime, tt.loc)
			if gotSlotID != tt.wantSlotID {
				t.Errorf("processSpecialDateAndTimeSlot() gotSlotID = %v, want %v", gotSlotID, tt.wantSlotID)
			}
			if gotValid != tt.wantValid {
				t.Errorf("processSpecialDateAndTimeSlot() gotValid = %v, want %v", gotValid, tt.wantValid)
			}
		})
	}
}

func TestCapacitySetting_GetTotalReservedBSAWeightUnitG(t *testing.T) {
	tests := []struct {
		name     string
		setting  CapacitySetting
		expected int64
	}{
		{
			name: "没有产品设置",
			setting: CapacitySetting{
				BSAWeight:       100.0, // 100kg
				ProductSettings: []ProductSetting{},
			},
			expected: 0, // 0g
		},
		{
			name: "单个产品30%预留",
			setting: CapacitySetting{
				BSAWeight: 100.0, // 100kg
				ProductSettings: []ProductSetting{
					{
						ProductID:  1001,
						Proportion: 30.0, // 30%
					},
				},
			},
			expected: 30000, // 30kg = 30000g
		},
		{
			name: "多个产品总共60%预留",
			setting: CapacitySetting{
				BSAWeight: 100.0, // 100kg
				ProductSettings: []ProductSetting{
					{
						ProductID:  1001,
						Proportion: 30.0, // 30%
					},
					{
						ProductID:  1002,
						Proportion: 20.0, // 20%
					},
					{
						ProductID:  1003,
						Proportion: 10.0, // 10%
					},
				},
			},
			expected: 60000, // 60kg = 60000g
		},
		{
			name: "所有产品总共100%预留",
			setting: CapacitySetting{
				BSAWeight: 50.0, // 50kg
				ProductSettings: []ProductSetting{
					{
						ProductID:  1001,
						Proportion: 50.0, // 50%
					},
					{
						ProductID:  1002,
						Proportion: 50.0, // 50%
					},
				},
			},
			expected: 50000, // 50kg = 50000g
		},
		{
			name: "零重量BSA",
			setting: CapacitySetting{
				BSAWeight: 0.0, // 0kg
				ProductSettings: []ProductSetting{
					{
						ProductID:  1001,
						Proportion: 100.0, // 100%
					},
				},
			},
			expected: 0, // 0g
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.setting.GetTotalReservedBSAWeightUnitG()
			if result != tt.expected {
				t.Errorf("GetTotalReservedBSAWeightUnitG() = %v, expected %v", result, tt.expected)
			}
		})
	}
}
