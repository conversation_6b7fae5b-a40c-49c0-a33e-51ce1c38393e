package entity

import (
	"context"
	"sort"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"

	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type LHCapacityConfig struct {
	ID                 int               `json:"id"`
	CapacityName       string            `json:"capacity_name"`
	ILHVendorName      string            `json:"ilh_vendor_name"`
	ILHLineID          string            `json:"ilh_line_id"`
	ILHLineName        string            `json:"ilh_line_name"`
	TWS                []string          `json:"tws"`
	DestinationPort    []string          `json:"destination_port"`
	DGType             rule.DGFlag       `json:"dg_type"`
	CapacitySettings   []CapacitySetting `json:"capacity_settings"`
	RuleStatus         rule.RuleStatus   `json:"status"`
	EffectiveStartTime int64             `json:"effective_start_time"`
	Operator           string            `json:"operator"`
}

// CapacitySetting 表示容量设置的详细信息
type CapacitySetting struct {
	TimeIntervalType TimeIntervalType `json:"time_interval_type"` // 时间间隔类型
	TimeObject       TimeObject       `json:"time_object"`        // 时间对象
	BSAWeight        float64          `json:"bsa_weight"`         // BSA重量(kg)
	AdhocWeight      float64          `json:"adhoc_weight"`       // 临时重量(kg)
	ReserveStatus    ReserveStatus    `json:"reserve_status"`     // 特殊产品保留容量状态
	ProductSettings  []ProductSetting `json:"product_settings"`   // 产品设置列表
}

// ProductSetting 表示产品分配设置
type ProductSetting struct {
	ProductID  int     `json:"product_id"` // 产品ID
	Proportion float64 `json:"proportion"` // 分配比例(%) (0~100)
}

// ILHCapacitySettingInfo 包含容量设置及匹配的TWS和DP信息
type ILHCapacitySettingInfo struct {
	CapacitySetting             CapacitySetting
	TWS                         []string
	DestPorts                   []string
	InheritanceTimeSlotCapacity []TimeSlotCapacity
}

type TimeSlotCapacity struct {
	SlotID        string
	BSACapacity   int64
	AdhocCapacity int64
}

// TimeObject 表示不同时间区间类型的时间对象
type TimeObject struct {
	// 对于默认设置 - Default
	IsAllTime bool `json:"is_all_time,omitempty"` // 是否为"All"时间

	// 对于每周容量 - Weekly Capacity
	WeekDays []timeutil.WeekDay `json:"week_days,omitempty"` // 星期几列表

	// 对于特殊日期 - Special Date
	// 使用格式为"2025-01-01"的字符串记录日期
	SpecialDates []string `json:"special_dates,omitempty"` // 特殊日期字符串数组，每个日期格式为"2025-01-01"

	// 对于特殊时间段 - Special Time Slot
	SpecialTimeSlotStartTime string `json:"special_time_slot_start_time,omitempty"` // 开始时间字符串，格式为"10:00:00"，表示每天的这个时间点，注意：该字符串不包含时区信息，解析时使用TWS配置的时区
	SpecialTimeSlotEndTime   string `json:"special_time_slot_end_time,omitempty"`   // 结束时间字符串，格式为"14:00:00"，表示每天的这个时间点，注意：该字符串不包含时区信息，解析时使用TWS配置的时区

	// 对于特殊日期+时间段 - Special Date + Time Slot
	SpecialDateAndTimeSlotStartTime string `json:"special_date_and_time_slot_start_time,omitempty"` // 日期时间开始时间字符串，格式为"2025-01-01 00:00:00"，注意：该字符串不包含时区信息，解析时使用TWS配置的时区
	SpecialDateAndTimeSlotEndTime   string `json:"special_date_and_time_slot_end_time,omitempty"`   // 日期时间结束时间字符串，格式为"2025-01-01 00:00:00"，注意：该字符串不包含时区信息，解析时使用TWS配置的时区
}

// 以下是各种辅助方法，用于创建和操作时间对象

// NewDefaultTimeObject 创建默认的"All"时间对象
func NewDefaultTimeObject() TimeObject {
	return TimeObject{
		IsAllTime: true,
	}
}

// NewWeeklyTimeObject 创建每周容量的时间对象
func NewWeeklyTimeObject(weekDays []timeutil.WeekDay) TimeObject {
	return TimeObject{
		WeekDays: weekDays,
	}
}

// NewSpecialDateFromString 直接从日期字符串创建特殊日期对象
func NewSpecialDateFromString(dateStr string) TimeObject {
	return TimeObject{
		SpecialDates: []string{dateStr},
	}
}

// GetSpecialDateEffectiveRanges 获取所有特殊日期的生效时间范围
// 返回开始和结束时间戳的切片
func (obj TimeObject) GetSpecialDateEffectiveRanges() [][2]int64 {
	if len(obj.SpecialDates) == 0 {
		return nil
	}

	result := make([][2]int64, 0, len(obj.SpecialDates))

	for _, dateStr := range obj.SpecialDates {
		// 从字符串解析日期
		// 注意：由于只有日期没有时间，这里默认使用UTC时区
		// 实际应用时应考虑使用正确的时区，与IsInTimeRange中使用的timezoneOffset参数保持一致
		t, err := timeutil.ParseDate(dateStr)
		if err != nil {
			continue
		}

		startTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.UTC)

		// 结束时间为开始时间+24小时
		endTime := startTime.Add(24 * time.Hour)

		result = append(result, [2]int64{startTime.Unix(), endTime.Unix()})
	}

	return result
}

// NewSpecialTimeSlotFromString 直接从时间字符串创建特殊时间段对象
func NewSpecialTimeSlotFromString(startTime, endTime string) TimeObject {
	return TimeObject{
		SpecialTimeSlotStartTime: startTime,
		SpecialTimeSlotEndTime:   endTime,
	}
}

// NewSpecialDateTimeSlotFromString 直接从日期时间字符串创建特殊日期时间段对象
func NewSpecialDateTimeSlotFromString(startDateTime, endDateTime string) TimeObject {
	return TimeObject{
		SpecialDateAndTimeSlotStartTime: startDateTime,
		SpecialDateAndTimeSlotEndTime:   endDateTime,
	}
}

// IsInTimeRange 判断给定时间戳是否在时间范围内
func (obj TimeObject) IsInTimeRange(timestamp int64, timezoneOffset int) bool {
	// 将传入的时间戳转换为当地时间
	loc := time.FixedZone("", timezoneOffset*timeutil.HourSecs)
	currentTime := time.Unix(timestamp, 0).In(loc)

	// 对于"All"时间
	if obj.IsAllTime {
		return true
	}

	// 对于每周容量
	if len(obj.WeekDays) > 0 {
		weekday := int(currentTime.Weekday())
		// 将Go的0转换为我们的7（对应Sunday）
		if weekday == 0 {
			weekday = 7
		}
		for _, day := range obj.WeekDays {
			if int(day) == weekday {
				return true
			}
		}
		return false
	}

	// 对于特殊日期
	if len(obj.SpecialDates) > 0 {
		timeRanges := obj.GetSpecialDateEffectiveRanges()
		for _, timeRange := range timeRanges {
			if timestamp >= timeRange[0] && timestamp <= timeRange[1] {
				return true
			}
		}
		return false
	}

	// 对于特殊时间段
	if obj.SpecialTimeSlotStartTime != "" && obj.SpecialTimeSlotEndTime != "" {
		// 解析开始和结束时间，注意：时间字符串不包含时区信息，需要使用传入的timezoneOffset
		startTimeParsed, errStart := timeutil.ParseTime(obj.SpecialTimeSlotStartTime)
		endTimeParsed, errEnd := timeutil.ParseTime(obj.SpecialTimeSlotEndTime)

		if errStart != nil || errEnd != nil {
			return false
		}

		// 确保结束时间在开始时间之后
		if endTimeParsed.Before(startTimeParsed) {
			return false // 不支持跨天情况
		}

		// 组合当前日期和时间段的时间，确保使用相同的时区
		startTimeToday := time.Date(
			currentTime.Year(), currentTime.Month(), currentTime.Day(),
			startTimeParsed.Hour(), startTimeParsed.Minute(), startTimeParsed.Second(),
			0, loc)

		endTimeToday := time.Date(
			currentTime.Year(), currentTime.Month(), currentTime.Day(),
			endTimeParsed.Hour(), endTimeParsed.Minute(), endTimeParsed.Second(),
			0, loc)

		// 判断当前时间是否在时间段内
		return (currentTime.Equal(startTimeToday) || currentTime.After(startTimeToday)) &&
			(currentTime.Equal(endTimeToday) || currentTime.Before(endTimeToday))
	}

	// 对于特殊日期+时间段
	if obj.SpecialDateAndTimeSlotStartTime != "" && obj.SpecialDateAndTimeSlotEndTime != "" {
		// 解析开始和结束时间，注意：时间字符串不包含时区信息，需要使用传入的timezoneOffset
		startTime, errStart := timeutil.ParseDateTimeInLoc(obj.SpecialDateAndTimeSlotStartTime, loc)
		endTime, errEnd := timeutil.ParseDateTimeInLoc(obj.SpecialDateAndTimeSlotEndTime, loc)

		if errStart != nil || errEnd != nil {
			return false
		}

		// 将时间转换为Unix时间戳，确保使用相同的时区
		startTimestamp := startTime.Unix()
		endTimestamp := endTime.Unix()

		return timestamp >= startTimestamp && timestamp <= endTimestamp
	}

	return false
}

func (s CapacitySetting) GetNonReservedBSAWeightUnitG() int64 {
	var reserveProportion float64
	for _, productSetting := range s.ProductSettings {
		reserveProportion += productSetting.Proportion
	}

	nonReserveProportion := (100 - reserveProportion) / 100

	return int64(s.BSAWeight * nonReserveProportion * 1000)
}

func (s CapacitySetting) GetProductReservedBSAWeightUnitG(productID int) (int64, bool) {
	var productReserveProportion float64
	for _, productSetting := range s.ProductSettings {
		if productID == productSetting.ProductID {
			productReserveProportion = productSetting.Proportion
		}
	}

	if productReserveProportion <= 0 {
		return 0, false
	}

	return int64(s.BSAWeight * (productReserveProportion / 100) * 1000), true
}

func (s CapacitySetting) GetBSAWeightUnitG() int64 {
	return int64(s.BSAWeight * 1000)
}

func (s CapacitySetting) GetAdhocWeightUnitG() int64 {
	return int64(s.AdhocWeight * 1000)
}

// GetTotalReservedBSAWeightUnitG 获取所有产品的预留BSA容量总和（以克为单位）
// 用于计算非预留BSA容量的实际使用量
func (s CapacitySetting) GetTotalReservedBSAWeightUnitG() int64 {
	var totalReservedProportion float64
	for _, productSetting := range s.ProductSettings {
		totalReservedProportion += productSetting.Proportion
	}

	// 计算所有产品预留容量的总和
	return int64(s.BSAWeight * (totalReservedProportion / 100) * 1000)
}

func GetHighestPriorityCapacitySetting(ctx context.Context, capacitySettings []CapacitySetting, tws string) (CapacitySetting, bool) {
	// 先按时间间隔类型排序，TimeIntervalType越大越优先
	sort.Slice(capacitySettings, func(i, j int) bool {
		return capacitySettings[i].TimeIntervalType > capacitySettings[j].TimeIntervalType
	})

	twsConfig, exist := configutil.GetTwsCutoffTimeWithTimezone(ctx)[tws]
	if !exist {
		return CapacitySetting{}, false
	}

	currentTime := timeutil.GetCurrentUnixTimeStamp(ctx)
	for _, capacitySetting := range capacitySettings {
		if capacitySetting.TimeObject.IsInTimeRange(currentTime, twsConfig.TimezoneOffset) {
			return capacitySetting, true
		}
	}

	return CapacitySetting{}, false
}

// filterTimeSlotSettings 过滤出所有时间段类型的容量设置
func filterTimeSlotSettings(capacitySettings []CapacitySetting) []CapacitySetting {
	var timeSlotSettings []CapacitySetting
	for _, setting := range capacitySettings {
		if setting.TimeIntervalType == TimeIntervalTypeSpecialTimeSlot ||
			setting.TimeIntervalType == TimeIntervalTypeSpecialDateAndTimeSlot {
			timeSlotSettings = append(timeSlotSettings, setting)
		}
	}
	return timeSlotSettings
}

// processSpecialTimeSlot 处理特殊时间段类型的容量设置
func processSpecialTimeSlot(setting CapacitySetting, currentTime, cutoffTime int64, loc *time.Location) (string, bool) {
	startTimeStr := setting.TimeObject.SpecialTimeSlotStartTime
	endTimeStr := setting.TimeObject.SpecialTimeSlotEndTime

	// 生成slotID
	slotID := GenerateSlotID(startTimeStr, endTimeStr)

	// 解析开始和结束时间，注意：这里的时间字符串不包含时区信息，使用传入的loc时区解析
	startTime, errStart := timeutil.ParseTime(startTimeStr)
	if errStart != nil {
		return "", false
	}

	endTime, errEnd := timeutil.ParseTime(endTimeStr)
	if errEnd != nil {
		return "", false
	}

	// 获取当前时间和cutoff时间对应的日期，确保使用相同的时区
	currentTimeObj := time.Unix(currentTime, 0).In(loc)
	cutoffTimeObj := time.Unix(cutoffTime, 0).In(loc)

	// 获取cutoff时间点（当天）
	todayCutoffTimePoint := time.Date(
		currentTimeObj.Year(), currentTimeObj.Month(), currentTimeObj.Day(),
		cutoffTimeObj.Hour(), cutoffTimeObj.Minute(), cutoffTimeObj.Second(),
		0, loc).Unix()

	// 获取cutoff时间点（前一天）
	yesterdayCutoffTimePoint := time.Date(
		currentTimeObj.Year(), currentTimeObj.Month(), currentTimeObj.Day(),
		cutoffTimeObj.Hour(), cutoffTimeObj.Minute(), cutoffTimeObj.Second(),
		0, loc).AddDate(0, 0, -1).Unix()

	// 确定基准时间 - 根据当前时间是否在今天的cutoff之前
	var referenceCutoffTime int64
	var referenceDate time.Time

	if currentTime < todayCutoffTimePoint {
		// 如果当前时间在今天的cutoff之前，使用昨天的cutoff时间
		referenceCutoffTime = yesterdayCutoffTimePoint
		referenceDate = time.Unix(yesterdayCutoffTimePoint, 0).In(loc)
	} else {
		// 如果当前时间在今天的cutoff之后，使用今天的cutoff时间
		referenceCutoffTime = todayCutoffTimePoint
		referenceDate = time.Unix(todayCutoffTimePoint, 0).In(loc)
	}

	// 将开始和结束时间应用到参考日期，确保在同一时区下计算
	startDateTime := time.Date(
		referenceDate.Year(), referenceDate.Month(), referenceDate.Day(),
		startTime.Hour(), startTime.Minute(), startTime.Second(),
		0, loc)

	endDateTime := time.Date(
		referenceDate.Year(), referenceDate.Month(), referenceDate.Day(),
		endTime.Hour(), endTime.Minute(), endTime.Second(),
		0, loc)

	// 修改后的匹配逻辑：
	// 1. 开始时间必须晚于或等于cutoff时间
	if startDateTime.Unix() < referenceCutoffTime {
		return "", false
	}

	// 2. 结束时间必须早于或等于当前时间
	if endDateTime.Unix() > currentTime {
		return "", false
	}

	// 3. 确保开始时间早于结束时间
	if startDateTime.Unix() > endDateTime.Unix() {
		return "", false
	}

	return slotID, true
}

// processSpecialDateAndTimeSlot 处理特殊日期+时间段类型的容量设置
func processSpecialDateAndTimeSlot(setting CapacitySetting, currentTime, cutoffTime int64, loc *time.Location) (string, bool) {
	startDateTimeStr := setting.TimeObject.SpecialDateAndTimeSlotStartTime
	endDateTimeStr := setting.TimeObject.SpecialDateAndTimeSlotEndTime

	// 注意：这里的时间字符串不包含时区信息，我们使用传入的loc时区来解析
	startDateTime, err := timeutil.ParseDateTimeInLoc(startDateTimeStr, loc)
	if err != nil {
		return "", false
	}
	endDateTime, err := timeutil.ParseDateTimeInLoc(endDateTimeStr, loc)
	if err != nil {
		return "", false
	}

	// 获取当前时间对象，确保使用相同的时区
	currentTimeObj := time.Unix(currentTime, 0).In(loc)
	cutoffTimeObj := time.Unix(cutoffTime, 0).In(loc)

	// 获取cutoff时间点（当天）
	todayCutoffTimePoint := time.Date(
		currentTimeObj.Year(), currentTimeObj.Month(), currentTimeObj.Day(),
		cutoffTimeObj.Hour(), cutoffTimeObj.Minute(), cutoffTimeObj.Second(),
		0, loc).Unix()

	// 获取cutoff时间点（前一天）
	yesterdayCutoffTimePoint := time.Date(
		currentTimeObj.Year(), currentTimeObj.Month(), currentTimeObj.Day(),
		cutoffTimeObj.Hour(), cutoffTimeObj.Minute(), cutoffTimeObj.Second(),
		0, loc).AddDate(0, 0, -1).Unix()

	// 确定基准cutoff时间 - 根据当前时间是否在今天的cutoff之前
	var referenceCutoffTime int64

	if currentTime < todayCutoffTimePoint {
		// 如果当前时间在今天的cutoff之前，使用昨天的cutoff时间
		referenceCutoffTime = yesterdayCutoffTimePoint
	} else {
		// 如果当前时间在今天的cutoff之后，使用今天的cutoff时间
		referenceCutoffTime = todayCutoffTimePoint
	}

	// 修改后的匹配逻辑：
	// 1. 开始时间必须晚于或等于cutoff时间
	if startDateTime.Unix() < referenceCutoffTime {
		return "", false
	}

	// 2. 结束时间必须早于或等于当前时间
	if endDateTime.Unix() > currentTime {
		return "", false
	}

	// 3. 确保开始时间早于结束时间
	if startDateTime.Unix() > endDateTime.Unix() {
		return "", false
	}

	// 提取时间部分生成slotID
	startTimePart := startDateTimeStr[11:19] // 提取HH:MM:SS部分
	endTimePart := endDateTimeStr[11:19]     // 提取HH:MM:SS部分
	return GenerateSlotID(startTimePart, endTimePart), true
}

// GetInheritanceTimeSlotCapacity 获取继承的时间段容量
func GetInheritanceTimeSlotCapacity(ctx context.Context, capacitySettings []CapacitySetting, tws string) []TimeSlotCapacity {
	// 1. 过滤出所有时间段类型的容量设置
	timeSlotSettings := filterTimeSlotSettings(capacitySettings)

	// 如果没有时间段类型的设置，返回空结果
	if len(timeSlotSettings) == 0 {
		return []TimeSlotCapacity{}
	}

	// 2. 获取当前时间和基于CutoffTime的开始时间
	twsConfig, exist := configutil.GetTwsCutoffTimeWithTimezone(ctx)[tws]
	if !exist {
		return []TimeSlotCapacity{}
	}

	// 使用twsConfig的方法获取时区对象
	loc := twsConfig.GetTimezone()

	// 使用twsConfig的方法获取基于CutoffTime的时间点
	currentTime := timeutil.GetCurrentUnixTimeStamp(ctx)
	cutoffTimePoint := twsConfig.GetCutoffTimePoint(ctx)

	// 3. 创建结果列表
	result := make([]TimeSlotCapacity, 0)

	// 4. 遍历所有时间段设置
	for _, setting := range timeSlotSettings {
		var slotID string
		var valid bool

		// 根据时间间隔类型处理
		switch setting.TimeIntervalType {
		case TimeIntervalTypeSpecialTimeSlot:
			slotID, valid = processSpecialTimeSlot(setting, currentTime, cutoffTimePoint, loc)

		case TimeIntervalTypeSpecialDateAndTimeSlot:
			slotID, valid = processSpecialDateAndTimeSlot(setting, currentTime, cutoffTimePoint, loc)

		default:
			continue
		}

		if !valid {
			continue
		}

		// 5. 创建TimeSlotCapacity并添加到结果中
		timeSlotCapacity := TimeSlotCapacity{
			SlotID:        slotID,
			BSACapacity:   setting.GetBSAWeightUnitG(),
			AdhocCapacity: setting.GetAdhocWeightUnitG(),
		}

		result = append(result, timeSlotCapacity)
	}

	return result
}
