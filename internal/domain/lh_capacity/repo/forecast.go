package repo

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

var (
	ForecastLHCapacityTabHook = &ForecastLHCapacityTab{}
)

// GetForecastLHCapacitiesByTaskID 获取指定任务ID的所有LH容量配置
func (r *LHCapacityRepoImpl) GetForecastLHCapacitiesByTaskID(ctx context.Context, taskID int) ([]*ForecastLHCapacityTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, ForecastLHCapacityTabHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	var capacities []*ForecastLHCapacityTab
	if err := db.Table(ForecastLHCapacityTabHook.TableName()).Where("task_id = ?", taskID).Find(&capacities).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return capacities, nil
}

// DeleteForecastLHCapacitiesByTaskID 删除指定任务ID的所有LH容量配置
func (r *LHCapacityRepoImpl) DeleteForecastLHCapacitiesByTaskID(ctx context.Context, taskID int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, ForecastLHCapacityTabHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	if err := db.Table(ForecastLHCapacityTabHook.TableName()).
		Where("task_id = ?", taskID).
		Delete(ForecastLHCapacityTabHook).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

// BatchSaveForecastLHCapacities 批量保存预测任务LH容量配置
func (r *LHCapacityRepoImpl) BatchSaveForecastLHCapacities(ctx context.Context, taskID int, capacities []*ForecastLHCapacityTab) *srerr.Error {
	if len(capacities) == 0 {
		return nil
	}

	db, err := dbutil.MasterDB(ctx, ForecastLHCapacityTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	// 先删除已有的容量配置
	if err := db.Table(ForecastLHCapacityTabHook.TableName()).
		Where("task_id = ?", taskID).
		Delete(ForecastLHCapacityTabHook).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	// 批量插入新的配置
	if err := db.Table(ForecastLHCapacityTabHook.TableName()).CreateInBatches(capacities, 1000).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}
