package repo

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// LHCapacityRepo LH容量配置仓储接口
type LHCapacityRepo interface {
	// CreateLHCapacity 创建LH容量配置
	CreateLHCapacity(ctx context.Context, config *entity.LHCapacityConfig) (int, *srerr.Error)

	// BatchCreateLHCapacity 批量创建LH容量配置
	BatchCreateLHCapacity(ctx context.Context, configs []*entity.LHCapacityConfig) *srerr.Error

	// UpdateLHCapacity 更新LH容量配置
	UpdateLHCapacity(ctx context.Context, config *entity.LHCapacityConfig) *srerr.Error

	// GetLHCapacityByID 根据ID获取LH容量配置
	GetLHCapacityByID(ctx context.Context, id int) (*entity.LHCapacityConfig, *srerr.Error)

	// ListLHCapacities 分页获取LH容量配置列表
	ListLHCapacities(ctx context.Context, ilhVendorName, lineID string, dgType rule.DGFlag, destinationPort, tws string, page, limit int) ([]*entity.LHCapacityConfig, int64, *srerr.Error)

	// DeleteLHCapacity 删除LH容量配置
	DeleteLHCapacity(ctx context.Context, id int) *srerr.Error

	// UpdateLHCapacityStatus 更新LH容量配置状态
	UpdateLHCapacityStatus(ctx context.Context, id int, status rule.RuleStatus, operator string, effectiveStartTime int64) *srerr.Error

	// GetLHCapacitiesByStatus 获取指定状态的LH容量配置
	GetLHCapacitiesByStatus(ctx context.Context, status rule.RuleStatus) ([]*entity.LHCapacityConfig, *srerr.Error)

	// BatchSaveForecastLHCapacities 批量保存预测任务LH容量配置
	BatchSaveForecastLHCapacities(ctx context.Context, taskID int, capacities []*ForecastLHCapacityTab) *srerr.Error

	// GetForecastLHCapacitiesByTaskID 获取指定任务ID的LH容量配置
	GetForecastLHCapacitiesByTaskID(ctx context.Context, taskID int) ([]*ForecastLHCapacityTab, *srerr.Error)

	// DeleteForecastLHCapacitiesByTaskID 删除指定任务ID的LH容量配置
	DeleteForecastLHCapacitiesByTaskID(ctx context.Context, taskID int) *srerr.Error
}

// LHCapacityRepoImpl LH容量配置仓储实现
type LHCapacityRepoImpl struct{}

// NewLHCapacityRepoImpl 创建仓储实现实例
func NewLHCapacityRepoImpl() *LHCapacityRepoImpl {
	return &LHCapacityRepoImpl{}
}

// CreateLHCapacity 创建LH容量配置
func (r *LHCapacityRepoImpl) CreateLHCapacity(ctx context.Context, config *entity.LHCapacityConfig) (int, *srerr.Error) {
	db, err := dbutil.MasterDB(ctx, LHCapacityTabHook)
	if err != nil {
		return 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var tab LHCapacityTab
	tab.ConvertFromEntity(config)

	if err := db.Table(LHCapacityTabHook.TableName()).Create(&tab).GetError(); err != nil {
		return 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return tab.ID, nil
}

// UpdateLHCapacity 更新LH容量配置
func (r *LHCapacityRepoImpl) UpdateLHCapacity(ctx context.Context, config *entity.LHCapacityConfig) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, LHCapacityTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	var tab LHCapacityTab
	tab.ConvertFromEntity(config)

	if err := db.Table(LHCapacityTabHook.TableName()).Save(&tab).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

// GetLHCapacityByID 根据ID获取LH容量配置
func (r *LHCapacityRepoImpl) GetLHCapacityByID(ctx context.Context, id int) (*entity.LHCapacityConfig, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, LHCapacityTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var tab LHCapacityTab
	if err := db.Table(LHCapacityTabHook.TableName()).Where("id = ?", id).First(&tab).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	config := tab.ConvertToEntity()
	return config, nil
}

// ListLHCapacities 分页获取LH容量配置列表
func (r *LHCapacityRepoImpl) ListLHCapacities(ctx context.Context, ilhVendorName, lineID string, dgType rule.DGFlag, destinationPort, tws string, page, limit int) ([]*entity.LHCapacityConfig, int64, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, LHCapacityTabHook)
	if err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var tabs []LHCapacityTab
	var total int64

	offset := (page - 1) * limit

	query := db.Table(LHCapacityTabHook.TableName())

	if ilhVendorName != "" {
		query = query.Where("ilh_vendor_name = ?", ilhVendorName)
	}

	if lineID != "" {
		query = query.Where("ilh_line_id = ?", lineID)
	}

	if dgType > 0 {
		query = query.Where("dg_type = ?", dgType)
	}

	if destinationPort != "" {
		// 使用JSON_CONTAINS来查询JSON数组中是否包含特定值
		query = query.Where("JSON_CONTAINS(destination_port, JSON_ARRAY(?))", destinationPort)
	}

	if tws != "" {
		// 使用JSON_CONTAINS来查询JSON数组中是否包含特定值
		query = query.Where("JSON_CONTAINS(tws, JSON_ARRAY(?))", tws)
	}

	if err := query.Count(&total).GetError(); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := query.
		Order("id DESC").
		Offset(offset).
		Limit(limit).
		Find(&tabs).GetError(); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	configs := make([]*entity.LHCapacityConfig, 0, len(tabs))
	for i := range tabs {
		config := tabs[i].ConvertToEntity()
		configs = append(configs, config)
	}

	return configs, total, nil
}

// DeleteLHCapacity 删除LH容量配置
func (r *LHCapacityRepoImpl) DeleteLHCapacity(ctx context.Context, id int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, LHCapacityTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(LHCapacityTabHook.TableName()).Where("id = ?", id).Delete(LHCapacityTabHook).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

// UpdateLHCapacityStatus 更新LH容量配置状态
func (r *LHCapacityRepoImpl) UpdateLHCapacityStatus(ctx context.Context, id int, status rule.RuleStatus, operator string, effectiveStartTime int64) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, LHCapacityTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	updates := map[string]interface{}{
		"rule_status":          status,
		"effective_start_time": effectiveStartTime,
		"operator":             operator,
	}

	if err := db.Table(LHCapacityTabHook.TableName()).Where("id = ?", id).Updates(updates).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

// BatchCreateLHCapacity 批量创建LH容量配置
func (r *LHCapacityRepoImpl) BatchCreateLHCapacity(ctx context.Context, configs []*entity.LHCapacityConfig) *srerr.Error {
	if len(configs) == 0 {
		return nil
	}

	db, err := dbutil.MasterDB(ctx, LHCapacityTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	tabs := make([]LHCapacityTab, 0, len(configs))
	for _, config := range configs {
		var tab LHCapacityTab
		tab.ConvertFromEntity(config)
		tabs = append(tabs, tab)
	}

	if err := db.Table(LHCapacityTabHook.TableName()).CreateInBatches(tabs, 1000).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

// GetLHCapacitiesByStatus 获取指定状态的LH容量配置
func (r *LHCapacityRepoImpl) GetLHCapacitiesByStatus(ctx context.Context, status rule.RuleStatus) ([]*entity.LHCapacityConfig, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, LHCapacityTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var tabs []LHCapacityTab
	if err := db.Table(LHCapacityTabHook.TableName()).
		Where("rule_status = ?", status).
		Find(&tabs).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	configs := make([]*entity.LHCapacityConfig, 0, len(tabs))
	for i := range tabs {
		config := tabs[i].ConvertToEntity()
		configs = append(configs, config)
	}

	return configs, nil
}
