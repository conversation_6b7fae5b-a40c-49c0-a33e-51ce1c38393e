package routing

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	entity2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
)

// calculateSingleLaneCapacityUsage 计算单一Lane情况下的容量使用分布
// 按照优先级顺序：ReservedBSA -> NonReservedBSA -> Adhoc
func (rs *ILHRoutingServiceImpl) calculateSingleLaneCapacityUsage(
	ctx context.Context,
	ilh string,
	input ILHSelectionInput,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
) (int64, int64, int64, string) {
	// 如果没有ILH线路ID或容量配置，则全部使用Adhoc
	if ilh == "" || len(ilhCapacitySettingMap) == 0 {
		logger.CtxLogInfof(ctx, "No ILH line ID or capacity settings found for single lane, using Adhoc capacity: order_weight=%d", input.OrderWeight)
		return 0, 0, input.OrderWeight, entity2.DefaultSlotID
	}

	// 获取ILH线路的容量配置
	capacitySettingInfo, ok := ilhCapacitySettingMap[ilh]
	if !ok {
		logger.CtxLogInfof(ctx, "No capacity setting found for ILH=%s in single lane, using Adhoc capacity: order_weight=%d", ilh, input.OrderWeight)
		return 0, 0, input.OrderWeight, entity2.DefaultSlotID
	}

	// 获取时间段ID
	slotID := capacitySettingInfo.GetRelevantSlotID(ctx, input.OrderTime)

	// 1. 尝试使用预留BSA容量
	// 获取预留BSA容量
	reservedBSACapacity, hasReserved := capacitySettingInfo.CapacitySetting.GetProductReservedBSAWeightUnitG(input.ProductID)
	if !hasReserved || reservedBSACapacity <= 0 {
		// 没有预留BSA容量，直接跳到非预留BSA容量分配
		logger.CtxLogDebugf(ctx, "No reserved BSA capacity for product=%d in ILH=%s", input.ProductID, ilh)
		nonReservedUsage, adhocUsage := rs.calculateRemainingCapacityUsage(ctx, ilh, input.DGType, input.OrderWeight, capacitySettingInfo, slotID)
		return 0, nonReservedUsage, adhocUsage, slotID
	}

	// 获取当前产品BSA用量
	productBSAWeight, err := rs.ilhWeightCounter.GetILHProductBSAWeight(ctx, input.ProductID, ilh, input.DGType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get product BSA weight for ILH=%s, product=%d: %v", ilh, input.ProductID, err)
		productBSAWeight = 0 // 出错时默认为0
	}

	// 计算剩余预留容量
	remainingReservedCapacity := int64(0)
	if reservedBSACapacity > productBSAWeight {
		remainingReservedCapacity = reservedBSACapacity - productBSAWeight
	}

	// 如果没有剩余预留容量，直接跳到非预留BSA容量分配
	if remainingReservedCapacity <= 0 {
		nonReservedUsage, adhocUsage := rs.calculateRemainingCapacityUsage(ctx, ilh, input.DGType, input.OrderWeight, capacitySettingInfo, slotID)
		return 0, nonReservedUsage, adhocUsage, slotID
	}

	// 如果预留容量足够，全部使用预留BSA
	if remainingReservedCapacity >= input.OrderWeight {
		logger.CtxLogInfof(ctx, "Using reserved BSA capacity for single lane ILH=%s: order_weight=%d, reserved_usage=%d",
			ilh, input.OrderWeight, input.OrderWeight)
		return input.OrderWeight, 0, 0, slotID
	}

	// 预留容量不足，部分使用预留，剩余部分继续下一步
	logger.CtxLogInfof(ctx, "Partially using reserved BSA capacity for single lane ILH=%s: reserved_usage=%d, remaining_weight=%d",
		ilh, remainingReservedCapacity, input.OrderWeight-remainingReservedCapacity)

	// 更新剩余需要分配的重量
	remainingWeight := input.OrderWeight - remainingReservedCapacity

	// 分配剩余部分
	nonReservedUsage, adhocUsage := rs.calculateRemainingCapacityUsage(ctx, ilh, input.DGType, remainingWeight, capacitySettingInfo, slotID)
	return remainingReservedCapacity, nonReservedUsage, adhocUsage, slotID
}

// calculateRemainingCapacityUsage 计算剩余容量的分配（非预留BSA和Adhoc）
func (rs *ILHRoutingServiceImpl) calculateRemainingCapacityUsage(
	ctx context.Context,
	ilh string,
	dgType int,
	remainingWeight int64,
	capacitySettingInfo entity2.ILHCapacitySettingInfo,
	slotID string,
) (int64, int64) {
	// 获取非预留BSA容量
	nonReservedBSACapacity := capacitySettingInfo.CapacitySetting.GetNonReservedBSAWeightUnitG()

	// 如果没有非预留BSA容量，全部使用Adhoc
	if nonReservedBSACapacity <= 0 {
		logger.CtxLogInfof(ctx, "No non-reserved BSA capacity for ILH=%s, using Adhoc: adhoc_usage=%d", ilh, remainingWeight)
		return 0, remainingWeight
	}

	// 获取当前BSA总用量
	totalBSAWeight, err := rs.ilhWeightCounter.GetILHBSAWeight(ctx, ilh, dgType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get total BSA weight for ILH=%s: %v", ilh, err)
		totalBSAWeight = 0 // 出错时默认为0
	}

	// 计算已使用的非预留BSA容量
	// 获取预留BSA总容量
	reservedBSATotal := capacitySettingInfo.CapacitySetting.GetTotalReservedBSAWeightUnitG()

	usedNonReservedBSA := totalBSAWeight - reservedBSATotal
	if usedNonReservedBSA < 0 {
		usedNonReservedBSA = 0
	}

	// 计算剩余非预留BSA容量
	remainingNonReservedCapacity := nonReservedBSACapacity - usedNonReservedBSA
	if remainingNonReservedCapacity <= 0 {
		// 非预留BSA容量已用完，全部使用Adhoc
		logger.CtxLogInfof(ctx, "Non-reserved BSA capacity exhausted for ILH=%s, using Adhoc: adhoc_usage=%d", ilh, remainingWeight)
		return 0, remainingWeight
	}

	// 如果非预留BSA容量足够
	if remainingNonReservedCapacity >= remainingWeight {
		logger.CtxLogInfof(ctx, "Using non-reserved BSA capacity for ILH=%s: non_reserved_usage=%d", ilh, remainingWeight)
		return remainingWeight, 0
	}

	// 非预留BSA容量不足，部分使用非预留BSA，剩余使用Adhoc
	adhocUsage := remainingWeight - remainingNonReservedCapacity
	logger.CtxLogInfof(ctx, "Split usage between non-reserved BSA and Adhoc for ILH=%s: non_reserved_usage=%d, adhoc_usage=%d",
		ilh, remainingNonReservedCapacity, adhocUsage)
	return remainingNonReservedCapacity, adhocUsage
}
