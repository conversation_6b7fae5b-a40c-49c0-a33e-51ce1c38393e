package rule

import (
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
)

type SmartRoutingOrderData struct {
	CodAmount           float64                      `json:"cod_amount"`
	Cogs                float64                      `json:"cogs"`
	CreateOrderTime     uint32                       `json:"create_order_time"`
	WmsFlag             uint8                        `json:"wms_flag"`
	DgFlag              uint8                        `json:"dg_flag"`
	ShipmentType        uint8                        `json:"shipment_type"`
	IsReturn            uint8                        `json:"is_return"`
	PickupLocationIds   []int                        `json:"pickup_location_ids"`
	PickupPostCode      string                       `json:"pickup_post_code"`
	DeliveryLocationIds []int                        `json:"delivery_location_ids"`
	DeliveryPostCode    string                       `json:"delivery_post_code"`
	Items               []*SmartRoutingOrderDataItem `json:"items"`
	ActualPointMap      map[string][]*pb.ActualPoint `json:"actual_point_map"`
	IsCod               bool                         `json:"is_cod"`
	TwsCode             string                       `json:"tws_code"`
	OrderWeight         int                          `json:"order_weight"`
	IsWms               bool                         `json:"is_wms"`
	ParcelQuantity      int                          // use for ILH Routing
	LmId                string                       // user for ILH Routing
	CCMode              pb.CCMode
}

type SmartRoutingOrderDataItem struct {
	ItemID     uint64  `json:"item_id"`
	ModelID    uint64  `json:"model_id"`
	CategoryID uint32  `json:"category_id"`
	Weight     float32 `json:"weight"`
	Height     float32 `json:"height"`
	Length     float32 `json:"length"`
	Width      float32 `json:"width"`
	Quantity   uint32  `json:"quantity"`
}

func (s SmartRoutingOrderData) IsCCRoutingMode() bool {
	return s.CCMode == pb.CCMode_CCRouting
}
