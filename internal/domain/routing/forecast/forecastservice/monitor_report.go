package forecastservice

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
)

const (
	Success    = true
	Failed     = false
	BreakPoint = "BreakPoint"
)

func HardCriteriaMonitorReport(ctx context.Context, monitorKey string, taskId int, data string, isSuccess bool) {
	if isSuccess {
		monitoring.ReportSuccess(ctx, monitoring.HardCriteriaMonitor, monitoring.GenerateLocalForecastMonitorKey(monitorKey, taskId), data)
	} else {
		monitoring.ReportError(ctx, monitoring.HardCriteriaMonitor, monitoring.GenerateLocalForecastMonitorKey(monitorKey, taskId), data)
	}
}
