package forecastservice

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"testing"
)

func TestServiceImpl_QueryOrderAggregation(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
	configutil.Init()
	ctx := context.TODO()
	//s := ServiceImpl{laneSrv: lane.NewLaneService(lfsclient.NewLfsApiImpl(), llsclient.NewLlsApiImpl()), address: address.NewAddrRepoImpl(locationclient.NewLocationClientImpl(layercache.NewLayerCache()))}
	//err := localcache.Init(lcregistry.LocalCacheConfig...)
	//if err != nil {
	//	panic(err)
	//}
	dbutil.Init()

	result := &persistent.HardCriteriaTaskTab{}
	err := dbutil.Select(ctx, persistent.HardCriteriaTaskHook, map[string]interface{}{
		"routing_type": 1,
	}, result)

	if err != nil {
		panic(err)
	}
	//req := QueryOrderAggregationRequest{
	//	ProductId:   91003,
	//	RoutingType: 2,
	//	StartDate:   "2023-02-22",
	//	EndDate:     "2023-02-23",
	//	WeightRange: []*persistent.WeightRange{
	//		{
	//			Min: 0, Max: 1000,
	//		},
	//	},
	//	Region: envvar.GetCID(),
	//}
	//
	//wrList := []*persistent.WeightRange{
	//	{
	//		Min: 0,
	//		Max: 1000,
	//	},
	//}
	//resp, aggErr := s.dataApi.OrderAggregation(ctx, dataclient.QueryOrderAggregationRequest{
	//	ProductId:   91003,
	//	StartDate:   "2023-02-22",
	//	EndDate:     "2023-02-23",
	//	RoutingType: 2,
	//	Region:      envvar.GetCID(),
	//	WeightRange: wrList,
	//})
	//if aggErr != nil {
	//	panic(aggErr)
	//}
	//request := foreschema.QueryOrderAggregationRequest{FmAble: true, LmAble: true, BuyerStateAble: true}
	//resultList := convertToResultTable(ctx, resp.Data.List)
	//
	//result, resultErr := s.OrderAggregation(ctx, request, resultList)
	//if resultErr != nil {
	//	panic(resultErr)
	//}
	//
	//println(fmt.Sprintf("%+v", result))
}
