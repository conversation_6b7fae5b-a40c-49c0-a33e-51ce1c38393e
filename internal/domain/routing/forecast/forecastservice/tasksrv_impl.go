package forecastservice

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	foreschema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"math"
	"time"
)

// GetFinishedHCTaskVolume 获取已完成硬性校验刷新动作的订单数量
func (m *ForecastTaskServiceImpl) GetFinishedHCTaskVolume(ctx context.Context, keyType string, taskId uint64, productId uint64, orderStartTime string, orderEndTime string) (int, *srerr.Error) {
	var (
		val int
		err error
	)
	key := GenFinishedHCTaskKey(keyType, taskId, productId, orderStartTime, orderEndTime)
	useNewRedis := configutil.GetHardCriteriaTaskConfig(ctx).UseNewRedis
	if useNewRedis {
		val, err = redisutil.GetInt(ctx, key)
	} else {
		val, err = redisutil.GetDefaultInstance().Get(ctx, key).Int()
	}

	if err != nil && err != redis.Nil {
		return 0, srerr.With(srerr.CodisErr, val, err)
	}

	return val, nil
}

// GetCompletionOrdersInfo 按天获取订单数据
func (m *ForecastTaskServiceImpl) GetCompletionOrdersInfo(ctx context.Context, taskId uint64, routingType int, productId int64, orderStartTime string, orderEndTime string) ([]foreschema.CompletionOrder, int, *srerr.Error) {
	if !configutil.GetDataApiSwitchConf(ctx).QueryOrderCountSwitch {
		return m.GeProgressByDay(ctx, taskId, routingType, productId, orderStartTime, orderEndTime)
	} else {
		return m.GeProgressByDayV2(ctx, taskId, routingType, productId, orderStartTime, orderEndTime)
	}
}

// 获取每天执行硬性校验的进度条
func (m *ForecastTaskServiceImpl) GeProgressByDay(ctx context.Context, taskId uint64, routingType int, productId int64, orderStartTime string, orderEndTime string) ([]foreschema.CompletionOrder, int, *srerr.Error) {
	orderCountList, err := m.DataApi.QueryOrderCount(ctx, uint64(productId), routingType, orderStartTime, orderEndTime)
	if err != nil {
		return nil, 0, err
	}
	if orderCountList.Retcode != 0 {
		return nil, 0, srerr.New(srerr.DataApiErr, nil, "query order count return err %+v", orderCountList.Message)
	}

	var result []foreschema.CompletionOrder
	finishedOrderCount := 0
	for _, orderCount := range orderCountList.Data.List {
		tempCompletionOrder := foreschema.CompletionOrder{
			OrderPaidTime:    orderCount.Date,
			ShipmentQuantity: uint64(orderCount.Count),
		}
		count, err := m.GetFinishedHCTaskVolume(ctx, forecast.PrefixCurrentDay, taskId, uint64(productId), orderCount.Date, orderCount.Date)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetHardCriteriaTaskDetail| get single day finished hard criteria order value err:%+v", err)
			result = append(result, tempCompletionOrder)
			continue
		}
		finishedOrderCount += count
		if tempCompletionOrder.ShipmentQuantity != 0 {
			tempCompletionOrder.Percentage = math.Floor(float64(count) / float64(tempCompletionOrder.ShipmentQuantity) * 100)
		}

		result = append(result, tempCompletionOrder)
	}

	return result, finishedOrderCount, nil
}

func (m *ForecastTaskServiceImpl) GeProgressByDayV2(ctx context.Context, taskId uint64, routingType int, productId int64, orderStartTime string, orderEndTime string) ([]foreschema.CompletionOrder, int, *srerr.Error) {
	orderCountList, err := m.DataApi.OrderAggregationV2(ctx, dataclient.QueryOrderAggregationRequest{
		ProductId:   int(productId),
		RoutingType: uint8(routingType),
		StartDate:   orderStartTime,
		EndDate:     orderEndTime,
		Region:      envvar.GetCID(),
	})
	if err != nil {
		return nil, 0, err
	}
	if orderCountList.RetCode != 0 {
		return nil, 0, srerr.New(srerr.DataApiErr, nil, "query order count return err %+v", orderCountList.Message)
	}

	var result []foreschema.CompletionOrder
	finishedOrderCount := 0
	for _, orderCount := range orderCountList.Data.List {
		tempCompletionOrder := foreschema.CompletionOrder{
			OrderPaidTime:    orderCount.OrderCreateDate,
			ShipmentQuantity: uint64(orderCount.Quantity),
		}
		count, err := m.GetFinishedHCTaskVolume(ctx, forecast.PrefixCurrentDay, taskId, uint64(productId), orderCount.OrderCreateDate, orderCount.OrderCreateDate)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetHardCriteriaTaskDetail| get single day finished hard criteria order value err:%+v", err)
			result = append(result, tempCompletionOrder)
			continue
		}
		finishedOrderCount += count
		if tempCompletionOrder.ShipmentQuantity != 0 {
			tempCompletionOrder.Percentage = math.Floor(float64(count) / float64(tempCompletionOrder.ShipmentQuantity) * 100)
		}

		result = append(result, tempCompletionOrder)
	}

	return result, finishedOrderCount, nil
}

func (m *ForecastTaskServiceImpl) clearTaskCountByDays(ctx context.Context, taskId uint64, productId uint64, orderStartTime string, orderEndTime string) *srerr.Error {
	//清空总的数据
	cErr := m.ClearFinishedHCTaskVolume(ctx, forecast.PrefixAllDay, taskId, productId, orderStartTime, orderEndTime)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTaskStatus|pid=%v, orderStartTime=%v, orderEndTime=%v clear count in redis err:%v", productId, orderStartTime, orderEndTime, cErr)
		return srerr.New(srerr.UpdateErr, nil, "UpdateHardCriteriaTaskStatus| clear all count in redis err:%v", cErr)
	}
	//获取日期列表
	ostObj, pErr := time.Parse(timeutil.DateFormat, orderStartTime)
	if pErr != nil {
		logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTaskStatus| parse start time err:%+v", pErr)
		return srerr.With(srerr.ParseTimeErr, nil, pErr)
	}
	oetObj, pErr := time.Parse(timeutil.DateFormat, orderEndTime)
	if pErr != nil {
		logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTaskStatus| parse end time err:%+v", pErr)
		return srerr.With(srerr.ParseTimeErr, nil, pErr)
	}
	days := getDaysList(ostObj, oetObj)
	//清空每一天的数据
	for _, day := range days {
		cErr := m.ClearFinishedHCTaskVolume(ctx, forecast.PrefixCurrentDay, taskId, productId, day.Format(timeutil.DateFormat), day.Format(timeutil.DateFormat))
		if cErr != nil {
			logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTaskStatus|pid=%v, orderStartTime=%v, orderEndTime=%v clear count in redis err:%v", productId, day.Format(timeutil.DateFormat), day.Format(timeutil.DateFormat), cErr)
			return srerr.New(srerr.UpdateErr, nil, "UpdateHardCriteriaTaskStatus| clear count by day:%v in redis err:%v", day, cErr)
		}
	}

	return nil
}

// ClearFinishedHCTaskVolume 清空硬性校验刷新任务的订单数量（redis）
func (m *ForecastTaskServiceImpl) ClearFinishedHCTaskVolume(ctx context.Context, keyType string, taskId uint64, productId uint64, orderStartTime string, orderEndTime string) *srerr.Error {
	key := GenFinishedHCTaskKey(keyType, taskId, productId, orderStartTime, orderEndTime)

	if err := redisutil.GetDefaultInstance().Set(ctx, key, 0, 0).Err(); err != nil {
		return srerr.With(srerr.CodisErr, nil, err)
	}

	return nil
}

// 仅Stopped/End/Done/Failed 状态可重新开始跑硬性校验, 其中End/Done/Failed需要从头开始跑
func isRestartFromHead(oldStatus int, newStatus int) bool {
	if (oldStatus == forecast.TaskTerminated || oldStatus == forecast.TaskDone ||
		oldStatus == forecast.TaskFailed) && newStatus == forecast.TaskPending {
		return true
	}

	return false
}

// checkTaskStatus :check task status in ('doing', 'done')
func (m *ForecastTaskServiceImpl) checkTaskStatus(ctx context.Context, taskStatus int, tasks []persistent.HardCriteriaTaskTab) {
	monitorCtx := monitoring.WithTransactionMsg(ctx)

	// 1. 系统任务  doing 状态的 ，7 天前的 改为 done , 因为预测 并不会用到 超出7days 的数据， 异常处理机制

	//get check time by status
	checkTime := getCheckTimeByStatus(ctx, taskStatus)
	if checkTime == 0 {
		return
	}

	for _, task := range tasks {
		//if last update time within checkTime, skip it
		if task.LastUpdateTime >= checkTime {
			continue
		}
		ostObj, pErr := timeutil.ParseLocalTime(timeutil.DateFormat, task.OrderStartTime)
		sevenDayLimit := timeutil.GetNextDayStartTime(ctx, -7) // get local unix time of the day a week ago

		if taskStatus == forecast.TaskDoing {
			task.TaskStatus = forecast.TaskStopped
			//only change sys task whose order start time is within 7days to 'pending'
			if pErr == nil && task.TaskType == forecast.TaskTypeCreatedBySystem &&
				ostObj.Unix() >= sevenDayLimit {
				task.TaskStatus = forecast.TaskPending
			}
		}
		if taskStatus == forecast.TaskDone {
			//only change sys task whose order start time is within 7days to 'pending'
			if pErr != nil || ostObj.Unix() < sevenDayLimit || task.TaskType != forecast.TaskTypeCreatedBySystem {
				continue
			}
			task.TaskStatus = forecast.TaskPending
		}

		if uErr := m.ForecastRepo.UpdateHardCriteriaTaskById(ctx, task.Id, taskStatus, task); uErr != nil {
			logger.CtxLogErrorf(ctx, "CheckHCTask|task id:%v, update 'done' task err%v", uErr)
			monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, fmt.Sprintf("CheckHCTask| task id:%v update done task status to 'pending', err:%v", task.Id, uErr.Error()))
			continue
		}
		logger.CtxLogInfof(ctx, "CheckHCTask| task id:%v, sys task has been changed its status, from %v, to %v, check time:%v", task.Id, taskStatus, task.TaskStatus, checkTime)
	}
}

func (m *ForecastTaskServiceImpl) checkPendingTask(ctx context.Context, tasks []persistent.HardCriteriaTaskTab) {
	monitorCtx := monitoring.WithTransactionMsg(ctx)

	exceed7Days := timeutil.GetNextDayStartTimeByCid(ctx, -7, envvar.GetCID())
	var needToUpdate []persistent.HardCriteriaTaskTab
	for _, task := range tasks {
		dateStr := timeutil.FormatLocalDatestamp(exceed7Days)
		if task.OrderEndTime < dateStr {
			needToUpdate = append(needToUpdate, task)
		}
	}
	var count int
	for _, task := range needToUpdate {
		task.TaskStatus = forecast.TaskDone
		if uErr := m.ForecastRepo.UpdateHardCriteriaTaskById(ctx, task.Id, forecast.TaskPending, task); uErr != nil {
			logger.CtxLogErrorf(ctx, "CheckHCTask|task id:%v, update 'done' task err%v", uErr)
			monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, fmt.Sprintf("CheckHCTask| task id:%v update pending task status to 'done', err:%v", task.Id, uErr.Error()))
			continue
		} else {
			count += 1
		}
	}
	logger.CtxLogInfof(ctx, "CheckHCTask|number of tasks changed to done:%v", count)
}

func getCheckTimeByStatus(ctx context.Context, taskStatus int) int64 {
	nowTime := recorder.Now(ctx).Unix()
	var checkTTL int64
	if taskStatus == forecast.TaskDoing {
		checkTTL = configutil.GetHardCriteriaTaskConfig(ctx).CheckTTL //time used to check 'doing' task's health
		logger.CtxLogInfof(ctx, "getCheckTimeByStatus| task status:%v, check ttl:%v", taskStatus, checkTTL)
		return nowTime - checkTTL //total seconds from now to 'checkTTL'
	}
	if taskStatus == forecast.TaskDone {
		checkTTL = configutil.GetHardCriteriaTaskConfig(ctx).CheckDoneTTL //time used to check 'done' task's health
		logger.CtxLogInfof(ctx, "getCheckTimeByStatus| task status:%v, check ttl:%v", taskStatus, checkTTL)
		return nowTime - checkTTL
	}

	return checkTTL
}

// 校验硬性校验task是否处于doing态
func (m *ForecastTaskServiceImpl) checkTaskInDoing(ctx context.Context, id uint64) bool {
	monitorCtx := monitor.AwesomeReportTransactionStart(ctx)
	//先判断task状态，非doing态直接跳过
	beginTime := recorder.Now(ctx).Unix()
	taskTab, tErr := m.ForecastRepo.GetHardCriteriaTask(ctx, map[string]interface{}{"id = ?": id})
	logger.CtxLogInfof(ctx, "RefreshHardCriteriaByTask|checkTaskInDoing|task id:%v, cost time:%v", id, recorder.Now(ctx).Unix()-beginTime)
	if len(taskTab) == 0 {
		logger.CtxLogInfof(ctx, "RefreshHardCriteriaByTask| get empty task list before starting batch check，id=%v", id)
		return false
	}
	if tErr != nil {
		logger.CtxLogErrorf(ctx, "RefreshHardCriteriaByTask| get task list before starting batch check, id=%v, err:%v", id, tErr)
		monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, tErr.Error())
		return false
	}
	if taskTab[0].TaskStatus != forecast.TaskDoing {
		logger.CtxLogInfof(ctx, "RefreshHardCriteriaByTask| task is not in 'doing', task:%+v", taskTab[0])
		return false
	}

	return true
}
