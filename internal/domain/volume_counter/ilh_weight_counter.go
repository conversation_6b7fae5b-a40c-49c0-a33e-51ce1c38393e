package volume_counter

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/go/go-redis"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

// ILH Smart Routing Revamp

type ILHWeightCounter interface {
	GetILHBSAWeight(ctx context.Context, lineID string, dgType int, twsCodes []string, destPorts []string, slotID string) (int64, *srerr.Error)
	IncrILHBSAWeight(ctx context.Context, lineID string, dgType int, twsCode string, destPort string, slotID string, weight int64) *srerr.Error
	GetILHAdhocWeight(ctx context.Context, lineID string, dgType int, twsCodes []string, destPorts []string, slotID string) (int64, *srerr.Error)
	IncrILHAdhocWeight(ctx context.Context, lineID string, dgType int, twsCode string, destPort string, slotID string, weight int64) *srerr.Error
	GetCCWeight(ctx context.Context, productID int, lineID string) (int64, *srerr.Error)
	IncrCCWeight(ctx context.Context, productID int, lineID string, weight int64) *srerr.Error
	GetILHProductBSAWeight(ctx context.Context, productID int, lineID string, dgType int, twsCodes []string, destPorts []string, slotID string) (int64, *srerr.Error)
	IncrILHProductBSAWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, slotID string, weight int64) *srerr.Error
	SaveOrderUsageInfo(ctx context.Context, input *ILHOrderUsageInfo) *srerr.Error
	GetOrderUsageInfo(ctx context.Context, packageNo string) (*ILHOrderUsageInfo, *srerr.Error)
}

type ILHWeightCounterImpl struct {
}

func NewILHWeightCounterImpl() *ILHWeightCounterImpl {
	return &ILHWeightCounterImpl{}
}

func (i *ILHWeightCounterImpl) GetILHBSAWeight(ctx context.Context, lineID string, dgType int, twsCodes []string, destPorts []string, slotID string) (int64, *srerr.Error) {
	timestamp := timeutil.GetCurrentUnixTimeStamp(ctx)
	keys := make([]string, 0, len(twsCodes)*len(destPorts))

	for _, twsCode := range twsCodes {
		for _, destPort := range destPorts {
			key := formatILHBSAWeightKey(ctx, lineID, dgType, twsCode, destPort, timestamp, slotID)
			keys = append(keys, key)
		}
	}

	if len(keys) == 0 {
		return 0, nil
	}

	vals, err := redisutil.GetDefaultInstance().MGet(ctx, keys...).Result()
	if err != nil {
		return 0, srerr.With(srerr.CodisErr, keys, err)
	}

	var totalWeight int64 = 0
	for _, val := range vals {
		if val == nil {
			continue
		}

		strVal, ok := val.(string)
		if !ok || strVal == "" {
			continue
		}

		intVal, err := strconv.Atoi(strVal)
		if err != nil {
			return 0, srerr.With(srerr.CodisErr, val, err)
		}

		totalWeight += int64(intVal)
	}

	return totalWeight, nil
}

func (i *ILHWeightCounterImpl) IncrILHBSAWeight(ctx context.Context, lineID string, dgType int, twsCode string, destPort string, slotID string, weight int64) *srerr.Error {
	key := formatILHBSAWeightKey(ctx, lineID, dgType, twsCode, destPort, timeutil.GetCurrentUnixTimeStamp(ctx), slotID)
	if err := redisutil.GetDefaultInstance().IncrBy(ctx, key, weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (i *ILHWeightCounterImpl) GetILHAdhocWeight(ctx context.Context, lineID string, dgType int, twsCodes []string, destPorts []string, slotID string) (int64, *srerr.Error) {
	timestamp := timeutil.GetCurrentUnixTimeStamp(ctx)
	keys := make([]string, 0, len(twsCodes)*len(destPorts))

	for _, twsCode := range twsCodes {
		for _, destPort := range destPorts {
			key := formatILHAdhocWeightKey(ctx, lineID, dgType, twsCode, destPort, timestamp, slotID)
			keys = append(keys, key)
		}
	}

	if len(keys) == 0 {
		return 0, nil
	}

	vals, err := redisutil.GetDefaultInstance().MGet(ctx, keys...).Result()
	if err != nil {
		return 0, srerr.With(srerr.CodisErr, keys, err)
	}

	var totalWeight int64 = 0
	for _, val := range vals {
		if val == nil {
			continue
		}

		strVal, ok := val.(string)
		if !ok || strVal == "" {
			continue
		}

		intVal, err := strconv.Atoi(strVal)
		if err != nil {
			return 0, srerr.With(srerr.CodisErr, val, err)
		}

		totalWeight += int64(intVal)
	}

	return totalWeight, nil
}

func (i *ILHWeightCounterImpl) IncrILHAdhocWeight(ctx context.Context, lineID string, dgType int, twsCode string, destPort string, slotID string, weight int64) *srerr.Error {
	key := formatILHAdhocWeightKey(ctx, lineID, dgType, twsCode, destPort, timeutil.GetCurrentUnixTimeStamp(ctx), slotID)
	if err := redisutil.GetDefaultInstance().IncrBy(ctx, key, weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (i *ILHWeightCounterImpl) GetCCWeight(ctx context.Context, productID int, lineID string) (int64, *srerr.Error) {
	key := formatCCWeightKey(productID, lineID, timeutil.GetCurrentUnixTimeStamp(ctx))
	weight, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil && !errors.Is(err, redis.Nil) {
		return 0, nil
	}

	return weight, nil
}

func (i *ILHWeightCounterImpl) IncrCCWeight(ctx context.Context, productID int, lineID string, weight int64) *srerr.Error {
	key := formatCCWeightKey(productID, lineID, timeutil.GetCurrentUnixTimeStamp(ctx))
	if err := redisutil.GetDefaultInstance().IncrBy(ctx, key, weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (i *ILHWeightCounterImpl) GetILHProductBSAWeight(ctx context.Context, productID int, lineID string, dgType int, twsCodes []string, destPorts []string, slotID string) (int64, *srerr.Error) {
	timestamp := timeutil.GetCurrentUnixTimeStamp(ctx)
	keys := make([]string, 0, len(twsCodes)*len(destPorts))

	for _, twsCode := range twsCodes {
		for _, destPort := range destPorts {
			key := formatILHProductBSAWeightKey(ctx, productID, lineID, dgType, twsCode, destPort, timestamp, slotID)
			keys = append(keys, key)
		}
	}

	if len(keys) == 0 {
		return 0, nil
	}

	vals, err := redisutil.GetDefaultInstance().MGet(ctx, keys...).Result()
	if err != nil {
		return 0, srerr.With(srerr.CodisErr, keys, err)
	}

	var totalWeight int64 = 0
	for _, val := range vals {
		if val == nil {
			continue
		}

		strVal, ok := val.(string)
		if !ok || strVal == "" {
			continue
		}

		intVal, err := strconv.Atoi(strVal)
		if err != nil {
			return 0, srerr.With(srerr.CodisErr, val, err)
		}

		totalWeight += int64(intVal)
	}

	return totalWeight, nil
}

func (i *ILHWeightCounterImpl) IncrILHProductBSAWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, slotID string, weight int64) *srerr.Error {
	key := formatILHProductBSAWeightKey(ctx, productID, lineID, dgType, twsCode, destPort, timeutil.GetCurrentUnixTimeStamp(ctx), slotID)
	if err := redisutil.GetDefaultInstance().IncrBy(ctx, key, weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (i *ILHWeightCounterImpl) SaveOrderUsageInfo(ctx context.Context, input *ILHOrderUsageInfo) *srerr.Error {
	usageInfoStr, err := jsoniter.MarshalToString(input)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to marshal ILHOrderUsageInfo for package %s: %v", input.PackageNo, err)
		return srerr.New(srerr.JsonErr, input, "failed to marshal usage info: %v", err)
	}

	key := formatILHOrderUsageKey(input.PackageNo)
	if err := redisutil.GetDefaultInstance().Set(ctx, key, usageInfoStr, ILHOrderUsageTTL).Err(); err != nil {
		logger.CtxLogErrorf(ctx, "Failed to save ILHOrderUsageInfo to Redis for package %s: %v", input.PackageNo, err)
		return srerr.With(srerr.CodisErr, key, err)
	}

	logger.CtxLogInfof(ctx, "Successfully saved ILHOrderUsageInfo for package %s, usageInfo: %s", input.PackageNo, usageInfoStr)
	return nil
}

func (i *ILHWeightCounterImpl) GetOrderUsageInfo(ctx context.Context, packageNo string) (*ILHOrderUsageInfo, *srerr.Error) {
	if packageNo == "" {
		return nil, srerr.New(srerr.ParamErr, nil, "packageNo is empty")
	}

	key := formatILHOrderUsageKey(packageNo)
	usageInfoStr, err := redisutil.GetDefaultInstance().Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			logger.CtxLogInfof(ctx, "No usage info found for packageNo: %s", packageNo)
			return nil, nil
		}
		return nil, srerr.With(srerr.CodisErr, key, err)
	}

	if usageInfoStr == "" {
		logger.CtxLogInfof(ctx, "No usage info found for packageNo: %s", packageNo)
		return nil, nil
	}

	var usageInfo ILHOrderUsageInfo
	if err := jsoniter.UnmarshalFromString(usageInfoStr, &usageInfo); err != nil {
		return nil, srerr.With(srerr.JsonErr, usageInfoStr, err)
	}

	return &usageInfo, nil
}

func formatILHBSAWeightKey(ctx context.Context, ilhLineID string, dgType int, twsCode string, destPort string, timestamp int64, slotID string) string {
	t := GetDateByTwsCode(ctx, twsCode, timestamp)
	dateStr := t.Format(constant.TimeLayout)
	if slotID != "" {
		return fmt.Sprintf("%s:%s:%d:%s:%s:%s:%s", ILHBSAWeightPrefix, ilhLineID, dgType, twsCode, destPort, dateStr, slotID)
	}
	return fmt.Sprintf("%s:%s:%d:%s:%s:%s", ILHBSAWeightPrefix, ilhLineID, dgType, twsCode, destPort, dateStr)
}

func formatILHAdhocWeightKey(ctx context.Context, ilhLineID string, dgType int, twsCode string, destPort string, timestamp int64, slotID string) string {
	t := GetDateByTwsCode(ctx, twsCode, timestamp)
	dateStr := t.Format(constant.TimeLayout)
	if slotID != "" {
		return fmt.Sprintf("%s:%s:%d:%s:%s:%s:%s", ILHAdhocWeightPrefix, ilhLineID, dgType, twsCode, destPort, dateStr, slotID)
	}
	return fmt.Sprintf("%s:%s:%d:%s:%s:%s", ILHAdhocWeightPrefix, ilhLineID, dgType, twsCode, destPort, dateStr)
}

func formatCCWeightKey(productID int, ccLineID string, timestamp int64) string {
	dateStr := timeutil.GetLocalTimeByTimestamp(timestamp).Format(constant.TimeLayout)
	return fmt.Sprintf("%s:%d:%s:%s", CCWeightPrefix, productID, ccLineID, dateStr)
}

func formatILHProductBSAWeightKey(ctx context.Context, productID int, ilhLineID string, dgType int, twsCode string, destPort string, timestamp int64, slotID string) string {
	t := GetDateByTwsCode(ctx, twsCode, timestamp)
	dateStr := t.Format(constant.TimeLayout)
	if slotID != "" {
		return fmt.Sprintf("%s:%s:%d:%d:%s:%s:%s:%s", ILHProductBSAWeightPrefix, ilhLineID, productID, dgType, twsCode, destPort, dateStr, slotID)
	}
	return fmt.Sprintf("%s:%s:%d:%d:%s:%s:%s", ILHProductBSAWeightPrefix, ilhLineID, productID, dgType, twsCode, destPort, dateStr)
}

func formatILHOrderUsageKey(packageNo string) string {
	return fmt.Sprintf("%s:%s", ILHOrderUsagePrefix, packageNo)
}

type ForecastILHWeightCounterImpl struct {
	counter   map[string]int64
	OrderTime int64
}

func NewForecastILHWeightCounterImpl() *ForecastILHWeightCounterImpl {
	return &ForecastILHWeightCounterImpl{
		counter: make(map[string]int64),
	}
}

func (f *ForecastILHWeightCounterImpl) SetOrderTime(orderTime int64) {
	f.OrderTime = orderTime
}

func (f *ForecastILHWeightCounterImpl) GetILHBSAWeight(ctx context.Context, lineID string, dgType int, twsCodes []string, destPorts []string, slotID string) (int64, *srerr.Error) {
	var totalWeight int64 = 0

	for _, twsCode := range twsCodes {
		for _, destPort := range destPorts {
			key := formatILHBSAWeightKey(ctx, lineID, dgType, twsCode, destPort, f.OrderTime, slotID)
			totalWeight += f.counter[key]
		}
	}

	return totalWeight, nil
}

func (f *ForecastILHWeightCounterImpl) IncrILHBSAWeight(ctx context.Context, lineID string, dgType int, twsCode string, destPort string, slotID string, weight int64) *srerr.Error {
	key := formatILHBSAWeightKey(ctx, lineID, dgType, twsCode, destPort, f.OrderTime, slotID)
	f.counter[key] += weight

	return nil
}

func (f *ForecastILHWeightCounterImpl) GetILHAdhocWeight(ctx context.Context, lineID string, dgType int, twsCodes []string, destPorts []string, slotID string) (int64, *srerr.Error) {
	var totalWeight int64 = 0

	for _, twsCode := range twsCodes {
		for _, destPort := range destPorts {
			key := formatILHAdhocWeightKey(ctx, lineID, dgType, twsCode, destPort, f.OrderTime, slotID)
			totalWeight += f.counter[key]
		}
	}

	return totalWeight, nil
}

func (f *ForecastILHWeightCounterImpl) IncrILHAdhocWeight(ctx context.Context, lineID string, dgType int, twsCode string, destPort string, slotID string, weight int64) *srerr.Error {
	key := formatILHAdhocWeightKey(ctx, lineID, dgType, twsCode, destPort, f.OrderTime, slotID)
	f.counter[key] += weight

	return nil
}

func (f *ForecastILHWeightCounterImpl) GetCCWeight(ctx context.Context, productID int, lineID string) (int64, *srerr.Error) {
	return f.counter[formatCCWeightKey(productID, lineID, f.OrderTime)], nil
}

func (f *ForecastILHWeightCounterImpl) IncrCCWeight(ctx context.Context, productID int, lineID string, weight int64) *srerr.Error {
	f.counter[formatCCWeightKey(productID, lineID, f.OrderTime)] += weight
	return nil
}

func (f *ForecastILHWeightCounterImpl) GetILHProductBSAWeight(ctx context.Context, productID int, lineID string, dgType int, twsCodes []string, destPorts []string, slotID string) (int64, *srerr.Error) {
	var totalWeight int64 = 0

	for _, twsCode := range twsCodes {
		for _, destPort := range destPorts {
			key := formatILHProductBSAWeightKey(ctx, productID, lineID, dgType, twsCode, destPort, f.OrderTime, slotID)
			totalWeight += f.counter[key]
		}
	}

	return totalWeight, nil
}

func (f *ForecastILHWeightCounterImpl) IncrILHProductBSAWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, slotID string, weight int64) *srerr.Error {
	key := formatILHProductBSAWeightKey(ctx, productID, lineID, dgType, twsCode, destPort, f.OrderTime, slotID)
	f.counter[key] += weight
	return nil
}

func (f *ForecastILHWeightCounterImpl) SaveOrderUsageInfo(ctx context.Context, input *ILHOrderUsageInfo) *srerr.Error {
	return nil
}

func (f *ForecastILHWeightCounterImpl) GetOrderUsageInfo(ctx context.Context, packageNo string) (*ILHOrderUsageInfo, *srerr.Error) {
	return nil, nil
}

func (f *ForecastILHWeightCounterImpl) GetCounter() map[string]int64 {
	return f.counter
}
