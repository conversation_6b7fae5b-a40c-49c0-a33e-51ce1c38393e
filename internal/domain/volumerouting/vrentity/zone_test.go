package vrentity

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestLocationZoneTree_Insert_Remove(t *testing.T) {
	tests := []struct {
		k          string
		removeList []*LocZoneImportInfo
		insertList []*LocZoneImportInfo
	}{
		{
			k: "insert normal",
			insertList: []*LocZoneImportInfo{
				{
					ActionMode:   1,
					Row:          1,
					ZoneName:     "ID1",
					LocId:        3,
					ParentLocIds: []int64{1, 2},
				},
			},
		},
		{
			k: "remove normal",
			removeList: []*LocZoneImportInfo{
				{
					ActionMode:   -1,
					Row:          1,
					ZoneName:     "ID1",
					LocId:        10,
					ParentLocIds: []int64{8, 9},
				},
			},
		},
		{
			k: "insert remove mix",
			removeList: []*LocZoneImportInfo{
				{
					ActionMode:   -1,
					Row:          1,
					ZoneName:     "ID1",
					LocId:        10,
					ParentLocIds: []int64{8, 9},
				},
			},
			insertList: []*LocZoneImportInfo{
				{
					ActionMode:   -1,
					Row:          1,
					ZoneName:     "ID1",
					LocId:        10,
					ParentLocIds: []int64{8, 9},
				},
			},
		},
		{
			k: "insert location duplicated",
			insertList: []*LocZoneImportInfo{
				{
					ActionMode:   -1,
					Row:          1,
					ZoneName:     "ID1",
					LocId:        10,
					ParentLocIds: []int64{8, 9},
				},
			},
		},
		{
			k: "insert location overlap",
			insertList: []*LocZoneImportInfo{
				{
					ActionMode:   -1,
					Row:          1,
					ZoneName:     "ID1",
					LocId:        9,
					ParentLocIds: []int64{8},
				},
			},
		},
		{
			k: "remove zone not found",
			removeList: []*LocZoneImportInfo{
				{
					ActionMode:   -1,
					Row:          1,
					ZoneName:     "ID2",
					LocId:        9,
					ParentLocIds: []int64{8},
				},
			},
		},
		{
			k: "remove location not found",
			removeList: []*LocZoneImportInfo{
				{
					ActionMode:   -1,
					Row:          1,
					ZoneName:     "ID1",
					LocId:        11,
					ParentLocIds: []int64{9, 10},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.k, func(t *testing.T) {
			tree := LocationZoneTree{
				GroupId: "ID001",
				LocSet: map[int64]*LocZoneImportInfo{
					10: {
						ZoneName:     "ID1",
						LocId:        10,
						ParentLocIds: []int64{8, 9},
					},
				},
				ZoneLocMap: map[string]map[int64]struct{}{
					"ID1": {10: struct{}{}},
				},
				ParentLocSet: map[int64]struct{}{8: {}, 9: {}},
			}
			switch tt.k {
			case "insert normal":
				err := tree.Insert(tt.insertList[0])
				assert.Nil(t, err)
				assert.EqualValues(t, 2, len(tree.LocSet))
				assert.Contains(t, tree.ZoneLocMap, "ID1")
				assert.EqualValues(t, 2, len(tree.ZoneLocMap["ID1"]))
			case "remove normal":
				err := tree.Remove(tt.removeList[0])
				assert.Nil(t, err)
				assert.EqualValues(t, 0, len(tree.LocSet))
				assert.EqualValues(t, 0, len(tree.ZoneLocMap))
				assert.EqualValues(t, 0, len(tree.ParentLocSet))
			case "insert remove mix":
				rerr := tree.Remove(tt.removeList[0])
				ierr := tree.Insert(tt.insertList[0])
				assert.Nil(t, rerr)
				assert.Nil(t, ierr)
				assert.EqualValues(t, 1, len(tree.LocSet))
				t.Logf("insert remove mix, ret:%s", objutil.JsonString(tree))
			case "insert location duplicated":
				err := tree.Insert(tt.insertList[0])
				assert.NotNil(t, err)
				assert.Equal(t, "location ID: 10 duplicated", err.Error())
			case "insert location overlap":
				err := tree.Insert(tt.insertList[0])
				assert.NotNil(t, err)
				assert.Contains(t, err.Error(), "overlapping")
			case "remove zone not found":
				err := tree.Remove(tt.removeList[0])
				assert.NotNil(t, err)
				assert.Equal(t, "zone: ID2 not found", err.Error())
			case "remove location not found":
				err := tree.Remove(tt.removeList[0])
				assert.NotNil(t, err)
				assert.Equal(t, "location: 11 not found", err.Error())
			}
		})
	}
}

func TestPostcodeZoneTree_Insert_Remove(t *testing.T) {
	tests := []struct {
		k          string
		removeList []*PostcodeZoneImportInfo
		insertList []*PostcodeZoneImportInfo
	}{
		{
			k: "insert normal",
			insertList: []*PostcodeZoneImportInfo{
				{
					ActionMode: 1,
					Row:        1,
					ZoneName:   "ID1",
					Postcode:   "002",
				},
			},
		},
		{
			k: "remove normal",
			removeList: []*PostcodeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					Postcode:   "001",
				},
			},
		},
		{
			k: "insert remove mix",
			removeList: []*PostcodeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					Postcode:   "001",
				},
			},
			insertList: []*PostcodeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					Postcode:   "001",
				},
			},
		},
		{
			k: "insert postcode duplicated",
			insertList: []*PostcodeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					Postcode:   "001",
				},
			},
		},
		{
			k: "remove zone not found",
			removeList: []*PostcodeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID2",
					Postcode:   "001",
				},
			},
		},
		{
			k: "remove postcode not found",
			removeList: []*PostcodeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					Postcode:   "003",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.k, func(t *testing.T) {
			tree := PostcodeZoneTree{
				GroupId: "ID001",
				PostcodeSet: map[string]*PostcodeZoneImportInfo{
					"001": {
						ZoneName: "ID1",
						Postcode: "001",
					},
				},
				ZonePostcodeMap: map[string]map[string]struct{}{
					"ID1": {"001": struct{}{}},
				},
			}
			switch tt.k {
			case "insert normal":
				err := tree.Insert(tt.insertList[0])
				assert.Nil(t, err)
				assert.EqualValues(t, 2, len(tree.PostcodeSet))
				assert.Contains(t, tree.ZonePostcodeMap, "ID1")
				assert.EqualValues(t, 2, len(tree.ZonePostcodeMap["ID1"]))
			case "remove normal":
				err := tree.Remove(tt.removeList[0])
				assert.Nil(t, err)
				assert.EqualValues(t, 0, len(tree.PostcodeSet))
				assert.EqualValues(t, 0, len(tree.ZonePostcodeMap))
			case "insert remove mix":
				rerr := tree.Remove(tt.removeList[0])
				ierr := tree.Insert(tt.insertList[0])
				assert.Nil(t, rerr)
				assert.Nil(t, ierr)
				assert.EqualValues(t, 1, len(tree.PostcodeSet))
				t.Logf("insert remove mix, ret:%s", objutil.JsonString(tree))
			case "insert postcode duplicated":
				err := tree.Insert(tt.insertList[0])
				assert.NotNil(t, err)
				assert.Equal(t, "postcode: 001 duplicated", err.Error())
			case "remove zone not found":
				err := tree.Remove(tt.removeList[0])
				assert.NotNil(t, err)
				assert.Equal(t, "zone: ID2 not found", err.Error())
			case "remove postcode not found":
				err := tree.Remove(tt.removeList[0])
				assert.NotNil(t, err)
				assert.Equal(t, "postcode: 003 not found", err.Error())
			}
		})
	}
}

func TestCepRangeZoneTree_Remove(t *testing.T) {
	tests := []struct {
		k          string
		removeList []*CepRangeZoneImportInfo
		insertList []*CepRangeZoneImportInfo
	}{
		{
			k: "empty insert normal",
			insertList: []*CepRangeZoneImportInfo{
				{
					ActionMode: 1,
					Row:        1,
					ZoneName:   "ID1",
					CepInitial: 1,
					CepFinal:   5,
				},
			},
		},
		{
			k: "insert normal left bound",
			insertList: []*CepRangeZoneImportInfo{
				{
					ActionMode: 1,
					Row:        1,
					ZoneName:   "ID1",
					CepInitial: 1,
					CepFinal:   5,
				},
			},
		},
		{
			k: "insert normal right bound",
			insertList: []*CepRangeZoneImportInfo{
				{
					ActionMode: 1,
					Row:        1,
					ZoneName:   "ID1",
					CepInitial: 200,
					CepFinal:   300,
				},
			},
		},
		{
			k: "remove normal",
			removeList: []*CepRangeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					CepInitial: 11,
					CepFinal:   15,
				},
			},
		},
		{
			k: "insert remove mix",
			removeList: []*CepRangeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					CepInitial: 11,
					CepFinal:   15,
				},
			},
			insertList: []*CepRangeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					CepInitial: 6,
					CepFinal:   10,
				},
			},
		},
		{
			k: "insert cep range overlap left",
			insertList: []*CepRangeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					CepInitial: 12,
					CepFinal:   18,
				},
			},
		},
		{
			k: "insert cep range overlap right",
			insertList: []*CepRangeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					CepInitial: 102,
					CepFinal:   106,
				},
			},
		},
		{
			k: "remove zone not found",
			removeList: []*CepRangeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID3",
					CepInitial: 6,
					CepFinal:   10,
				},
			},
		},
		{
			k: "remove cep initial not found",
			removeList: []*CepRangeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					CepInitial: 2,
					CepFinal:   15,
				},
			},
		},
		{
			k: "remove cep final not found",
			removeList: []*CepRangeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					CepInitial: 11,
					CepFinal:   6,
				},
			},
		},
		{
			k: "remove zone not match",
			removeList: []*CepRangeZoneImportInfo{
				{
					ActionMode: -1,
					Row:        1,
					ZoneName:   "ID1",
					CepInitial: 101,
					CepFinal:   105,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.k, func(t *testing.T) {
			tree := CepRangeZoneTree{
				GroupId: "ID001",
				CepRangeSet: map[int64]*CepRangeZoneImportInfo{
					11: {
						ZoneName:   "ID1",
						CepInitial: 11,
						CepFinal:   15,
					},
					101: {
						ZoneName:   "ID2",
						CepInitial: 101,
						CepFinal:   105,
					},
				},
				SortedInitialList: &[]int64{11, 101},
				ZoneCepRangeMap: map[string]map[int64]struct{}{
					"ID1": {11: {}},
					"ID2": {101: {}},
				},
			}
			switch tt.k {
			case "empty insert normal":
				tree2 := CepRangeZoneTree{
					GroupId:           "ID001",
					CepRangeSet:       make(map[int64]*CepRangeZoneImportInfo),
					SortedInitialList: &[]int64{},
					ZoneCepRangeMap:   make(map[string]map[int64]struct{}),
				}
				err := tree2.Insert(tt.insertList[0])
				assert.Nil(t, err)
				assert.Contains(t, tree2.CepRangeSet, int64(1))
				assert.EqualValues(t, 1, len(*tree2.SortedInitialList))
				assert.EqualValues(t, 1, len(tree2.ZoneCepRangeMap))
				t.Logf("tree is: %s\n", objutil.JsonString(tree2))
			case "insert normal left bound":
				err := tree.Insert(tt.insertList[0])
				assert.Nil(t, err)
				assert.EqualValues(t, 3, len(tree.CepRangeSet))
				assert.Contains(t, tree.ZoneCepRangeMap, "ID1")
				assert.EqualValues(t, 2, len(tree.ZoneCepRangeMap["ID1"]))
				t.Logf("tree is: %s\n", objutil.JsonString(tree))
			case "insert normal right bound":
				err := tree.Insert(tt.insertList[0])
				assert.Nil(t, err)
				assert.EqualValues(t, 3, len(tree.CepRangeSet))
				assert.Contains(t, tree.ZoneCepRangeMap, "ID1")
				assert.EqualValues(t, 2, len(tree.ZoneCepRangeMap["ID1"]))
				t.Logf("tree is: %s\n", objutil.JsonString(tree))
			case "remove normal":
				err := tree.Remove(tt.removeList[0])
				assert.Nil(t, err)
				assert.EqualValues(t, 1, len(tree.CepRangeSet))
				assert.EqualValues(t, 1, len(tree.ZoneCepRangeMap))
				t.Logf("tree is: %s\n", objutil.JsonString(tree))
			case "insert remove mix":
				rerr := tree.Remove(tt.removeList[0])
				ierr := tree.Insert(tt.insertList[0])
				assert.Nil(t, rerr)
				assert.Nil(t, ierr)
				assert.EqualValues(t, 2, len(tree.CepRangeSet))
				t.Logf("insert remove mix, ret: %s\n", objutil.JsonString(tree))
			case "insert cep range overlap left":
				err := tree.Insert(tt.insertList[0])
				assert.NotNil(t, err)
				assert.Contains(t, err.Error(), "cep range overlap")
				t.Logf("insert fail, err: %s\n", err.Error())
			case "insert cep range overlap right":
				err := tree.Insert(tt.insertList[0])
				assert.NotNil(t, err)
				assert.Contains(t, err.Error(), "cep range overlap")
				t.Logf("insert fail, err: %s\n", err.Error())
			case "remove zone not found":
				err := tree.Remove(tt.removeList[0])
				assert.NotNil(t, err)
				assert.Equal(t, "zone: ID3 not found", err.Error())
				t.Logf("remove fail, err: %s\n", err.Error())
			case "remove cep initial not found":
				err := tree.Remove(tt.removeList[0])
				assert.NotNil(t, err)
				assert.Contains(t, err.Error(), "cep initial not found")
				t.Logf("remove fail, err: %s\n", err.Error())
			case "remove cep final not found":
				err := tree.Remove(tt.removeList[0])
				assert.NotNil(t, err)
				assert.Contains(t, err.Error(), "cep final not found")
				t.Logf("remove fail, err: %s\n", err.Error())
			case "remove zone not match":
				err := tree.Remove(tt.removeList[0])
				assert.NotNil(t, err)
				assert.Contains(t, err.Error(), "zone not match")
				t.Logf("remove fail, err: %s\n", err.Error())
			}
		})
	}
}
