package schedule_stat

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"strings"
)

type MaskingScheduleVisualStatV2 struct {
	RedisClient   *redis.Client
	StatisticMap  map[string]int64
	BusinessIdMap map[string]interface{}
}

func NewMaskingScheduleVisualStatV2(redisClient *redis.Client) *MaskingScheduleVisualStatV2 {
	statisticMap := make(map[string]int64)
	businessIdMap := make(map[string]interface{})
	maskingScheduleVisualStatV2 := &MaskingScheduleVisualStatV2{
		RedisClient:   redisClient,
		StatisticMap:  statisticMap,
		BusinessIdMap: businessIdMap,
	}
	Register(AllocateV2, maskingScheduleVisualStatV2)
	return maskingScheduleVisualStatV2
}

func (m *MaskingScheduleVisualStatV2) ScheduleResultAnalyze(ctx context.Context, log interface{}, businessId string, businessType int) (map[string]map[string]int64, []string, *srerr.Error) {
	if businessId == "" {
		businessId = timeutil.FormatDateTimeByFormat(timeutil.GetLocalTime(ctx), MaskingScheduleStatHourTimeFormat)
	}
	result := make(map[string]map[string]int64)
	//1. 类型转换
	logDetail, ok := log.(*allocation.LogDetail)
	if !ok {
		return nil, nil, nil
	}
	//2. 遍历处理log_detail
	var scheduleDimensionList []string
	currentResultMap, finalResultMap := ParseAllocateLogV2(logDetail)
	// currentResult stat
	currentKey := fmt.Sprintf(KeyPattern, businessType, businessId, logDetail.MaskProductId, CurrentResult.String())
	result[currentKey] = currentResultMap
	// finalResult stat
	finalKey := fmt.Sprintf(KeyPattern, businessType, businessId, logDetail.MaskProductId, FinalResult.String())
	result[finalKey] = finalResultMap
	// 记录schedule dimension
	scheduleDimensionList = append(scheduleDimensionList, strconv.FormatInt(int64(logDetail.MaskProductId), 10))

	return result, scheduleDimensionList, nil
}

func (m *MaskingScheduleVisualStatV2) SaveScheduleStat(ctx context.Context, statResult map[string]map[string]int64, scheduleDimensionList []string, businessType int, businessId string) *srerr.Error {
	lock.Lock()
	//0. 保存businessId
	m.BusinessIdMap[businessId] = nil

	//1. 保存数据到内存
	for key, value := range statResult {
		for field, num := range value {
			newKey := fmt.Sprintf("%v%v%v%v%v", MaskingSyncMapStatPrefix+businessId, MaskingSyncMapSeparator, key, MaskingSyncMapSeparator, field)
			if statValue, exist := m.StatisticMap[newKey]; exist {
				m.StatisticMap[newKey] = statValue + num
			} else {
				m.StatisticMap[newKey] = num
			}
		}
	}

	//2. 记录运力key的key
	for _, scheduleDimension := range scheduleDimensionList {
		key := fmt.Sprintf(ScheduleDimensionKeyPattern, timeutil.FormatDateTimeByFormat(timeutil.GetLocalTime(ctx), MaskingScheduleStatHourTimeFormat))
		field := fmt.Sprintf(ScheduleDimensionFieldPattern, businessType, businessId, scheduleDimension)
		newKey := fmt.Sprintf("%v%v%v%v%v", MaskingSyncMapDimensionPrefix+businessId, MaskingSyncMapSeparator, key, MaskingSyncMapSeparator, field)
		m.StatisticMap[newKey] = 1
	}
	lock.Unlock()
	return nil
}

func (m *MaskingScheduleVisualStatV2) SyncScheduleStat(ctx context.Context, businessIdParam string) *srerr.Error {
	var redisGain int
	lock.Lock()
	pipeline := m.RedisClient.Pipeline()
	for businessId := range m.BusinessIdMap {
		for key, value := range m.StatisticMap {
			newKey := fmt.Sprintf("%v", key)
			if strings.HasPrefix(newKey, MaskingSyncMapStatPrefix+businessId) {
				keyList := strings.Split(newKey, MaskingSyncMapSeparator)
				if len(keyList) == MaskingSyncMapLen {
					pipeline.HIncrBy(ctx, keyList[1], keyList[2], value)
					// 设置超时时间为2小时
					pipeline.Expire(ctx, key, MaskingKeyTimeOut)
					redisGain++
				}
				delete(m.StatisticMap, key)
			}
			if strings.HasPrefix(newKey, MaskingSyncMapDimensionPrefix+businessId) {
				keyList := strings.Split(newKey, MaskingSyncMapSeparator)
				if len(keyList) == MaskingSyncMapLen {
					pipeline.HSet(ctx, keyList[1], keyList[2], value)
					// 设置超时时间为2小时
					pipeline.Expire(ctx, keyList[1], ScheduleDimensionKeyTimeOut)
					redisGain++
				}
				delete(m.StatisticMap, key)
			}
		}
		delete(m.BusinessIdMap, businessId)
	}
	lock.Unlock()

	if _, err := pipeline.Exec(ctx); err != nil {
		logger.CtxLogErrorf(ctx, "execute sync schedule status pipeline fail | err=%v", err)
	}

	logger.CtxLogDebugf(ctx, "save schedule visual to redis gain is : %d", redisGain)

	return nil
}
