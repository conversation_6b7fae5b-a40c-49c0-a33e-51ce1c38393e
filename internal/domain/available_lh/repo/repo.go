package repo

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// AvailableLHRepo 可用LH仓储接口
type AvailableLHRepo interface {
	// CreateAvailableLH 创建一条可用LH规则
	CreateAvailableLH(ctx context.Context, availableLH *entity.AvailableLH) (int, *srerr.Error)

	// UpdateAvailableLH 更新一条可用LH规则
	UpdateAvailableLH(ctx context.Context, availableLH *entity.AvailableLH) *srerr.Error

	// GetAvailableLH 获取指定ID的可用LH规则
	GetAvailableLH(ctx context.Context, id int) (*entity.AvailableLH, *srerr.Error)

	// ListAvailableLH 分页获取可用LH规则列表
	ListAvailableLH(ctx context.Context, multiProductID int, ruleStatus *rule.RuleStatus, page, limit int) ([]*entity.AvailableLH, int64, *srerr.Error)

	// DeleteAvailableLH 删除指定ID的可用LH规则
	DeleteAvailableLH(ctx context.Context, id int) *srerr.Error

	// UpdateAvailableLHStatus 更新可用LH规则的状态
	UpdateAvailableLHStatus(ctx context.Context, availableLH *entity.AvailableLH) *srerr.Error

	// GetAvailableLHsByStatus 获取指定状态的可用LH规则配置
	GetAvailableLHsByStatus(ctx context.Context, status rule.RuleStatus) ([]*entity.AvailableLH, *srerr.Error)

	// BatchSaveForecastAvailableLHRules 批量保存ILH预测任务的可用LH规则
	BatchSaveForecastAvailableLHRules(ctx context.Context, taskID int, forecasts []*ForecastAvailableLHTab) *srerr.Error

	// GetForecastAvailableLHRulesByTaskID 获取ILH预测任务的可用LH规则
	GetForecastAvailableLHRulesByTaskID(ctx context.Context, taskID int) ([]*ForecastAvailableLHTab, *srerr.Error)

	// DeleteForecastAvailableLHRulesByTaskID 删除ILH预测任务的可用LH规则
	DeleteForecastAvailableLHRulesByTaskID(ctx context.Context, taskID int) *srerr.Error
}

// AvailableLHRepoImpl 可用LH规则仓储实现
type AvailableLHRepoImpl struct{}

// NewAvailableLHRepoImpl 创建仓储实现实例
func NewAvailableLHRepoImpl() *AvailableLHRepoImpl {
	return &AvailableLHRepoImpl{}
}

// CreateAvailableLH 创建一条可用LH规则
func (r *AvailableLHRepoImpl) CreateAvailableLH(ctx context.Context, availableLH *entity.AvailableLH) (int, *srerr.Error) {
	db, err := dbutil.MasterDB(ctx, AvailableLHTabTabHook)
	if err != nil {
		return 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	// 将领域模型转换为数据库模型
	var tab AvailableLHTab
	tab.ConvertFromEntity(availableLH)

	if err := db.Table(AvailableLHTabTabHook.TableName()).Save(&tab).GetError(); err != nil {
		return 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return tab.ID, nil
}

// UpdateAvailableLH 更新一条可用LH规则
func (r *AvailableLHRepoImpl) UpdateAvailableLH(ctx context.Context, availableLH *entity.AvailableLH) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, AvailableLHTabTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	// 将领域模型转换为数据库模型
	var tab AvailableLHTab
	tab.ConvertFromEntity(availableLH)

	if err := db.Table(AvailableLHTabTabHook.TableName()).Save(&tab).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

// GetAvailableLH 获取指定ID的可用LH规则
func (r *AvailableLHRepoImpl) GetAvailableLH(ctx context.Context, id int) (*entity.AvailableLH, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, AvailableLHTabTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var tab AvailableLHTab
	if err := db.Table(AvailableLHTabTabHook.TableName()).Where("id = ?", id).First(&tab).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	// 将数据库模型转换为领域模型
	return tab.ConvertToEntity(), nil
}

// ListAvailableLH 分页获取可用LH规则列表
func (r *AvailableLHRepoImpl) ListAvailableLH(
	ctx context.Context, multiProductID int, ruleStatus *rule.RuleStatus, page, limit int,
) ([]*entity.AvailableLH, int64, *srerr.Error) {

	db, err := dbutil.SlaveDB(ctx, AvailableLHTabTabHook)
	if err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var tabs []*AvailableLHTab
	query := db.Table(AvailableLHTabTabHook.TableName())

	if multiProductID > 0 {
		query = query.Where("multi_product_id = ?", multiProductID)
	}

	if ruleStatus != nil {
		query = query.Where("rule_status = ?", *ruleStatus)
	}

	// 获取符合条件的总记录数
	var total int64
	if err := query.Count(&total).GetError(); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	offset := (page - 1) * limit
	if err := query.Order("id DESC").Offset(offset).Limit(limit).Find(&tabs).GetError(); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	// 将数据库模型列表转换为领域模型列表
	result := make([]*entity.AvailableLH, len(tabs))
	for i, tab := range tabs {
		result[i] = tab.ConvertToEntity()
	}

	return result, total, nil
}

// GetForecastAvailableLHRulesByTaskID 获取ILH预测任务的可用LH规则
func (r *AvailableLHRepoImpl) GetForecastAvailableLHRulesByTaskID(ctx context.Context, taskID int) ([]*ForecastAvailableLHTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, ForecastAvailableLHTabHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	// 查询规则
	var forecastAvailableLHs []*ForecastAvailableLHTab
	if err := db.Table(ForecastAvailableLHTabHook.TableName()).Where("task_id = ?", taskID).Find(&forecastAvailableLHs).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, taskID, err)
	}

	return forecastAvailableLHs, nil
}

// DeleteForecastAvailableLHRulesByTaskID 删除ILH预测任务的可用LH规则
func (r *AvailableLHRepoImpl) DeleteForecastAvailableLHRulesByTaskID(ctx context.Context, taskID int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, ForecastAvailableLHTabHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	// 删除规则
	if err := db.Table(ForecastAvailableLHTabHook.TableName()).Where("task_id = ?", taskID).Delete(ForecastAvailableLHTabHook).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, taskID, err)
	}

	return nil
}

// DeleteAvailableLH 删除指定ID的可用LH规则
func (r *AvailableLHRepoImpl) DeleteAvailableLH(ctx context.Context, id int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, AvailableLHTabTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(AvailableLHTabTabHook.TableName()).Where("id = ?", id).Delete(AvailableLHTab{}).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

// UpdateAvailableLHStatus 更新可用LH规则的状态
func (r *AvailableLHRepoImpl) UpdateAvailableLHStatus(ctx context.Context, availableLH *entity.AvailableLH) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, AvailableLHTabTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	updates := map[string]interface{}{
		"rule_status":          availableLH.RuleStatus,
		"effective_start_time": availableLH.EffectiveStartTime,
		"operator":             availableLH.Operator,
	}

	if err := db.Table(AvailableLHTabTabHook.TableName()).Where("id = ?", availableLH.ID).Updates(updates).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

// GetAvailableLHsByStatus 获取指定状态的可用LH规则配置
func (r *AvailableLHRepoImpl) GetAvailableLHsByStatus(ctx context.Context, status rule.RuleStatus) ([]*entity.AvailableLH, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, AvailableLHTabTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var tabs []*AvailableLHTab
	if err := db.Table(AvailableLHTabTabHook.TableName()).Where("rule_status = ?", status).Find(&tabs).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	// 将数据库模型列表转换为领域模型列表
	result := make([]*entity.AvailableLH, len(tabs))
	for i, tab := range tabs {
		result[i] = tab.ConvertToEntity()
	}

	return result, nil
}

// BatchSaveForecastAvailableLHRules 批量保存ILH预测任务的可用LH规则
func (r *AvailableLHRepoImpl) BatchSaveForecastAvailableLHRules(ctx context.Context, taskID int, forecasts []*ForecastAvailableLHTab) *srerr.Error {
	if forecasts == nil || len(forecasts) == 0 {
		return nil
	}

	db, err := dbutil.MasterDB(ctx, ForecastAvailableLHTabHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	// 删除现有规则
	if err := db.Table(ForecastAvailableLHTabHook.TableName()).Where("task_id = ?", taskID).Delete(ForecastAvailableLHTabHook).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, taskID, err)
	}

	// 创建新规则
	if err := db.Table(ForecastAvailableLHTabHook.TableName()).CreateInBatches(forecasts, 1000).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, forecasts, err)
	}

	return nil
}
