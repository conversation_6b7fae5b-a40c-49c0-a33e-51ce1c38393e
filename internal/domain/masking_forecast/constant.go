package masking_forecast

const (
	TaskConfigStatusDraft        = 1
	TaskConfigStatusProcess      = 2
	TaskConfigStatusComplete     = 3
	TaskConfigStatusFailed       = 4
	TaskConfigStatusDeploying    = 5
	TaskConfigStatusDeployFailed = 6
	TaskConfigStatusDeployed     = 7
)

const (
	FeeRuleStatusActive   = 1
	FeeRuleStatusUpcoming = 2
	FeeRuleStatusDraft    = 3
	FeeRuleStatusPast     = 4
	FeeRuleStatusForecast = 7
)

const (
	HistoricalRank = 1
	ForecastRank   = 2
)

const (
	Overall   = "Overall"
	ShopGroup = "ShopGroup"
	Zone      = "Zone"
	Route     = "Route"
	Blocked   = "Blocked"
	Sum       = "Sum"
)

var AllocateForecastRankTypeEnum = map[string]int{
	Overall:   0,
	ShopGroup: 1,
	Zone:      2,
	Route:     3,
	Blocked:   4,
	Sum:       5,
}

const (
	AllRankCode  = "All"
	AllTableName = "Overall Allocation Result"
)

const (
	Both  = 0
	NoWms = 1
	Wms   = 2
)

const (
	ForecastBlockedStatus  = 0
	ForecastCompleteStatus = 1
)

const (
	LocVolumeTypeRoute   = 1
	LocVolumeTypeZone    = 2
	LocVolumeTypeCountry = 3
)

const (
	WaitToSyncConfigStatus   = 1
	CompleteSyncConfigStatus = 2
	FailedToSyncConfigStatus = 3
	NoNeedToSyncConfigStatus = 4
)
const MaxConcurrentWorkForecastTask = 5

const MaxNumOfRankTableList = 10

const AllocateForecastTaskLockKey = "allocate_forecast_task_lock_key_%v"

const DeployForecastTaskLockKey = "deploy_forecast_task_lock_key_%v"

const DefaultShopGroupId int64 = -1

const (
	DeployFailedDesc         = "Deployment failed module is: %v"
	ProductPriorityModule    = "Shop Group &Product Priority and Toggle"
	SoftCriteriaModule       = "Soft Criteria"
	LocalVolumeRoutingModule = "Local Volume Routing"
	ShippingFeeModule        = "Allocation Shipping Fee"
	ClientModule             = "Client"
)
