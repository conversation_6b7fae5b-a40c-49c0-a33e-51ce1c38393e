package product

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

type ProdRepo interface {
	GetProductName(ctx context.Context, productId int64) string
	GetProductBaseInfoList(ctx context.Context) map[string]interface{}
	GetProductSellerMap(ctx context.Context) map[int64]string
}

type ProdRepoImpl struct {
}

func NewProdRepoImpl() *ProdRepoImpl {
	return &ProdRepoImpl{}
}

func (p *ProdRepoImpl) GetProductName(ctx context.Context, productId int64) string {
	k := strconv.FormatInt(productId, 10)
	result, err := localcache.Get(ctx, constant.ProductNameDict, k)
	if err != nil {
		return ""
	}
	return result.(string)
}

func (p *ProdRepoImpl) GetProductBaseInfoList(ctx context.Context) map[string]interface{} {
	result := localcache.AllItems(ctx, constant.ProductBaseInfoList)

	return result
}

func DumpProductBaseInfo() (map[string]interface{}, error) {
	lpsApi := lpsclient.NewLpsApiImpl()
	productList, err := lpsApi.GetProductBaseInfoList(context.TODO())
	if err != nil {
		return nil, err
	}
	result := make(map[string]interface{})
	for _, product := range productList {
		result[strconv.Itoa(product.ProductId)] = product
	}
	return result, nil
}

func DumpMaskingProductRef() (map[string]interface{}, error) {
	lpsApi := lpsclient.NewLpsApiImpl()
	maskingProductRefList, err := lpsApi.GetMaskingProductRefList(context.TODO())
	if err != nil {
		return nil, err
	}
	result := make(map[string]interface{})
	for _, maskingProductRef := range maskingProductRefList {
		result[strconv.Itoa(maskingProductRef.MaskingProductId)] = maskingProductRef
	}
	return result, nil
}

func DumpGroupCodeList() (map[string]interface{}, error) {
	ctx := context.TODO()
	lpsApi := lpsclient.NewLpsApiImpl()
	maskRuleVolumeRepoImpl := rulevolume.NewMaskRuleVolumeRepoImpl(lpsApi)
	maskRuleVolumeTabList, err := maskRuleVolumeRepoImpl.GetActiveRuleVolumes(ctx, rule_mode.MplOrderRule, allocation.SingleAllocate)
	if err != nil {
		return nil, err
	}
	for _, maskRuleVolume := range maskRuleVolumeTabList {
		if err := maskRuleVolume.UnmarshalGroupInfo(); err != nil {
			return nil, err
		}
	}

	// 再查Combination的
	condition := map[string]interface{}{
		"mask_combination_mode = ?": true,
		"rule_status = ?":           rulevolume.MaskRuleVolumeStatusActive,
		"effective_start_time <= ?": timeutil.GetCurrentUnixTimeStamp(ctx),
		"rule_mode = ?":             rule_mode.MplOrderRule,
	}
	tempList, err := maskRuleVolumeRepoImpl.GetRuleVolumesByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	for _, maskRuleVolume := range tempList {
		if err := maskRuleVolume.UnmarshalGroupInfo(); err != nil {
			return nil, err
		}
	}
	maskRuleVolumeTabList = append(maskRuleVolumeTabList, tempList...)

	groupCodeListMap := make(map[string]bool)
	for _, maskRuleVolumeTab := range maskRuleVolumeTabList {
		for _, fulfillmentProductGroupItem := range maskRuleVolumeTab.GroupInfo.FulfillmentProductGroupInfos {
			groupCodeListMap[fulfillmentProductGroupItem.GroupCode] = true
		}
	}

	// 遍历 map，将键添加到切片中
	result := make(map[string]interface{})
	for key := range groupCodeListMap {
		result[key] = true
	}

	return result, nil
}

func DumpProductLaneCode() (map[string]interface{}, error) {
	lpsApi := lpsclient.NewLpsApiImpl()
	laneCodeInfoList, err := lpsApi.GetLaneCodes(context.TODO())
	if err != nil {
		return nil, err
	}
	result := make(map[string]interface{})
	for _, laneCodeInfo := range laneCodeInfoList {
		result[strconv.Itoa(laneCodeInfo.ProductId)] = laneCodeInfo
	}
	return result, nil
}

func (p *ProdRepoImpl) GetProductSellerMap(ctx context.Context) map[int64]string {
	productSellerNameMap := make(map[int64]string)
	productBaseInfoList := p.GetProductBaseInfoList(ctx)
	if len(productBaseInfoList) == 0 {
		logger.CtxLogErrorf(ctx, "convertToMaskingKeyDataList|get empty product base info list")
		return productSellerNameMap
	}
	for productIdStr, productInterface := range productBaseInfoList {
		//convert product base info
		productBaseInfo, ok := productInterface.(*lpsclient.LogisticProductTab)
		if !ok {
			logger.CtxLogErrorf(ctx, "product info:%+v, convert product base info err", productInterface)
			continue
		}
		//set product id and seller display name
		productId, err := strconv.ParseInt(productIdStr, 10, 64)
		if err != nil {
			logger.CtxLogErrorf(ctx, "product key:%s, info:%+v, convert product id to int64 err: %s", productIdStr, productInterface, err.Error())
			continue
		}
		productSellerNameMap[productId] = productBaseInfo.SellerDisplayName

	}
	return productSellerNameMap
}

func DumpProductNameMap() (map[string]interface{}, error) {
	lpsApi := lpsclient.NewLpsApiImpl()
	productNameMap, err := lpsApi.GetAllProductIdNameList(context.TODO())
	if err != nil {
		return nil, err
	}
	result := make(map[string]interface{})
	for product, name := range productNameMap {
		result[strconv.FormatInt(product, 10)] = name
	}
	return result, nil
}
