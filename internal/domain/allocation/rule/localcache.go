package rule

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

func DumpMaskAllocationRule() (map[string]interface{}, error) {
	condition := map[string]interface{}{
		"rule_status = ?":           MaskRuleStatusActive,
		"effective_start_time <= ?": timeutil.GetCurrentUnixTimeStampUnWrapped(),
	}
	var records []*MaskAllocationRuleTab
	if err := dbutil.Select(context.Background(), MaskAllocationRuleHook, condition, &records, dbutil.WithOrder("effective_start_time DESC")); err != nil {
		return nil, err
	}

	data := make(map[string]interface{}, 0)
	for _, record := range records {
		var allocationMethod int64
		//兼容存量数据 allocation method = 0的也是single allocate
		if record.AllocationMethod == allocation.BatchAllocate {
			allocationMethod = allocation.BatchAllocate
		} else {
			allocationMethod = allocation.SingleAllocate
		}
		key := rule_mode.RuleMode(record.RuleMode).String() + strconv.FormatInt(record.MaskProductId, 10) + ":" + strconv.FormatInt(allocationMethod, 10)
		existData, exist := data[key]
		if exist {
			existRecord := existData.(*MaskAllocationRuleTab)
			if record.EffectiveStartTime > existRecord.EffectiveStartTime {
				data[key] = record
			}
		} else {
			data[key] = record
		}
	}

	return data, nil
}
