package rulevolume

type (
	MaskLocVolumeType    int
	MaskRuleVolumeStatus int
	MaskRuleStatus       int
	MaskRuleLimitType    uint8
)

const (
	LocVolumeTypeRoute        MaskLocVolumeType = 1
	LocVolumeTypeZone         MaskLocVolumeType = 2
	LocVolumeTypeCountry      MaskLocVolumeType = 3
	LocVolumeTypeRouteAndZone MaskLocVolumeType = 4

	MaskRuleVolumeStatusUncompleted MaskRuleVolumeStatus = 0
	MaskRuleVolumeStatusDraft       MaskRuleVolumeStatus = 1
	MaskRuleVolumeStatusQueuing     MaskRuleVolumeStatus = 2
	MaskRuleVolumeStatusActive      MaskRuleVolumeStatus = 3
	//MaskRuleVolumeStatusExpired     MaskRuleVolumeStatus = 4 // used in schedule_rule
	MaskRuleVolumeStatusPendingApproval MaskRuleVolumeStatus = 5

	// DefaultMaskMaxCapacity 解析location volume excel的时候，用户未填写数值时设置的默认值
	MinimumVolume          = 0
	DefaultMaskMaxCapacity = 999999999
	DefaultMaskMinVolume   = 999999999
	DefaultHardCap         = false

	RuleStatusUncompleted     MaskRuleStatus = 0
	RuleStatusDraft           MaskRuleStatus = 1
	RuleStatusQueuing         MaskRuleStatus = 2
	RuleStatusActive          MaskRuleStatus = 3
	RuleStatusExpired         MaskRuleStatus = 4
	RuleStatusPendingApproval MaskRuleStatus = 5

	VolumeSceneCapacity = "capacity"
	VolumeSceneTarget   = "target"

	//capacity && target 统一枚举类型
	BaVolumeTypeZone   = 1
	BaVolumeTypeRoute  = 2
	BaVolumeTypeRegion = 3

	// 配置Limit控制单量的维度
	MaskRuleLimitTypeFulfillmentProduct MaskRuleLimitType = 0 // 默认项
	MaskRuleLimitTypeGroup              MaskRuleLimitType = 1
)

func (v MaskLocVolumeType) String() string {
	switch v {
	case LocVolumeTypeRoute:
		return "Route"
	case LocVolumeTypeZone:
		return "Zone"
	case LocVolumeTypeCountry:
		return "Country"
	case LocVolumeTypeRouteAndZone:
		return "Zone&Route"
	}

	return "Unknown"
}

func (v MaskLocVolumeType) IsValidMaskLocVolumeType() bool {
	return v == LocVolumeTypeRoute || v == LocVolumeTypeZone || v == LocVolumeTypeCountry || v == LocVolumeTypeRouteAndZone
}

func (s MaskRuleVolumeStatus) Type() string {
	switch s {
	case MaskRuleVolumeStatusUncompleted:
		return "Uncompleted"
	case MaskRuleVolumeStatusDraft:
		return "Draft"
	case MaskRuleVolumeStatusQueuing:
		return "Queueing"
	case MaskRuleVolumeStatusActive:
		return "Active"
	case MaskRuleVolumeStatusPendingApproval:
		return "PendingApproval"
	}
	return ""
}
