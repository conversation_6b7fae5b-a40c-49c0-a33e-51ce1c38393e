package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	uuid "github.com/satori/go.uuid"
)

type SplitBatchAllocateOrdersTask struct {
	SplitBatchServer service.SplitBatchServer
	lpsApi           lpsclient.LpsApi
}

func NewSplitBatchAllocateOrdersTask(SplitBatchServer service.SplitBatchServer,
	lpsApi lpsclient.LpsApi) *SplitBatchAllocateOrdersTask {
	return &SplitBatchAllocateOrdersTask{
		SplitBatchServer: SplitBatchServer,
		lpsApi:           lpsApi,
	}
}

func (t *SplitBatchAllocateOrdersTask) Name() string {
	return constant.TaskSplitBatchAllocateOrders
}

func (t *SplitBatchAllocateOrdersTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	requestID := uuid.NewV4().String()
	ctx = logger.NewLogContext(ctx, requestID)
	ctx = requestid.SetToCtx(ctx, requestID)

	logger.CtxLogInfof(ctx, "SplitBatchAllocateOrdersTask|start|ShardingParam=%s|sharding no=%v|sharding total=%v", args.ShardingParam, args.ShardingNo, args.TotalShardings)

	// 根据Apollo配置匹配saturn切片序号
	maskProductID := 0
	for key, value := range configutil.GetBatchAllocateConf().MaskProductTableMapping {
		if value == int(args.ShardingNo) {
			maskProductID = key
			break
		}
	}
	// 匹配失败，返回
	if maskProductID == 0 {
		logger.CtxLogErrorf(ctx, "SplitBatchAllocateOrdersTask|fail to map mask sharding no:%v", args.ShardingNo)
		return nil
	}

	//productList, gErr := t.lpsApi.GetProductBaseInfoList(ctx)
	//if gErr != nil {
	//	logger.CtxLogErrorf(ctx, "SplitBatchAllocateOrdersTask|get all product from lps err:%v", gErr)
	//	return gErr
	//}
	//flag := false
	//for _, product := range productList {
	//	if product.ProductId == maskProductID {
	//		flag = true
	//		break
	//	}
	//}
	//if !flag {
	//	// 上报mask not exist
	//	prometheusutil.IncrBatchAllocateCounter(prometheusutil.CounterInfo{
	//		MaskProductID: args.ShardingParam,
	//		Scene:         prometheusutil.SplitBatch,
	//		Field:         prometheusutil.MaskNotExist,
	//		FieldValue:    "1",
	//	})
	//	logger.CtxLogErrorf(ctx, "SplitBatchAllocateOrdersTask| mask product:%v from saturn is illegal", maskProductID)
	//	return errors.New("mask product from saturn is illegal")
	//}

	if err := t.SplitBatchServer.SplitOrdersIntoBatch(ctx, uint64(maskProductID)); err != nil {
		logger.CtxLogErrorf(ctx, "SplitBatchAllocateOrdersTask|split batch err:%v", err)
		return err
	}

	return nil
}
