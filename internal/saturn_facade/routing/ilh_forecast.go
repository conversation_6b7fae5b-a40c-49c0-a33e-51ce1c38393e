package routing

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/smart_routing_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/ilh_smart_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/ilh_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	uuid "github.com/satori/go.uuid"
)

type ILHForecastTask struct {
	ilhForecastTaskService ilh_forecast_task.ILHForecastTaskService
	forecastService        smart_routing_forecast.SmartRoutingForecastService
}

func NewILHForecastTask(
	ilhForecastTaskService ilh_forecast_task.ILHForecastTaskService,
	routingService routing.RoutingService,
	forecastService smart_routing_forecast.SmartRoutingForecastService,
) *ILHForecastTask {
	return &ILHForecastTask{
		ilhForecastTaskService: ilhForecastTaskService,
		forecastService:        forecastService,
	}
}

func (p *ILHForecastTask) Name() string {
	return constant.TaskNameILHRoutingForecast
}

func (p *ILHForecastTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	// 1. 获取waiting状态的tasks
	listReq := &ilh_smart_routing.ListILHForecastTaskReq{
		TaskStatus: persistent.TaskStatusWaiting,
		Pageno:     1,
		Limit:      1, // 只获取第一个任务
	}

	listResp, err := p.ilhForecastTaskService.List(ctx, listReq)
	if err != nil {
		logger.CtxLogErrorf(ctx, "failed to list ilh forecast task: %v", err)
		return err
	}

	// 检查是否有waiting状态的task
	if listResp == nil || len(listResp.List) == 0 {
		// 没有waiting状态的任务，直接返回
		logger.CtxLogInfof(ctx, "no waiting ilh forecast task")
		return nil
	}

	// 2. 选择第一个task
	task := listResp.List[0]
	requestID := fmt.Sprintf("%s|task_id=%d", uuid.NewV4().String(), task.ID) // nolint
	ctx = logger.NewLogContext(ctx, requestID)
	ctx = requestid.SetToCtx(ctx, requestID)

	// 3. 先将任务状态改为处理中
	if err = p.ilhForecastTaskService.UpdateTaskStatus(ctx, task.ID, persistent.TaskStatusProcessing); err != nil {
		return err
	}

	// 处理task的逻辑
	logger.CtxLogInfof(ctx, "start processing ilh forecast task: %d", task.ID)

	// 获取任务详情
	taskResp, err := p.ilhForecastTaskService.Get(ctx, task.ID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "failed to get ilh forecast task details: %d, error: %v", task.ID, err)
		// 获取失败，更新任务状态为Failed
		if updateErr := p.ilhForecastTaskService.UpdateTaskStatus(ctx, task.ID, persistent.TaskStatusFailed); updateErr != nil {
			logger.CtxLogErrorf(ctx, "failed to update ilh forecast task status to failed: %v", updateErr)
		}
		return err
	}

	// 构造ILHForecastTask对象，填充完整信息
	ilhTask := smart_routing_forecast.ILHForecastTask{
		ID:                  task.ID,
		StartDate:           taskResp.StartDate,
		EndDate:             taskResp.EndDate,
		ShipmentResource:    taskResp.ShipmentResource,
		AvailableLHRuleList: taskResp.AvailableLHRuleList,
		LHCapacityList:      taskResp.LHCapacityList,
	}

	// 处理任务
	if err = p.forecastService.StartILHForecast(ctx, ilhTask); err != nil {
		logger.CtxLogErrorf(ctx, "failed to process ilh forecast task: %d, error: %v", task.ID, err)
		// 处理失败，更新任务状态为Failed
		if updateErr := p.ilhForecastTaskService.UpdateTaskStatus(ctx, task.ID, persistent.TaskStatusFailed); updateErr != nil {
			logger.CtxLogErrorf(ctx, "failed to update ilh forecast task status to failed: %v", updateErr)
		}
		return err
	}
	logger.CtxLogInfof(ctx, "successfully processed ilh forecast task: %d", task.ID)

	// 4. 处理完成后更新task状态为Done
	if err = p.ilhForecastTaskService.UpdateTaskStatus(ctx, task.ID, persistent.TaskStatusDone); err != nil {
		return err
	}

	// 5. 更新DeployStatus为Pending
	if err = p.ilhForecastTaskService.UpdateDeployStatus(ctx, task.ID, persistent.DeployStatusPending); err != nil {
		logger.CtxLogErrorf(ctx, "failed to update deploy status to pending: %d, error: %v", task.ID, err)
		// 这个错误不阻止主流程继续，因为预测已经完成
	}

	return nil
}
