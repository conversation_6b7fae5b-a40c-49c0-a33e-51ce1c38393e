package routing

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/smart_routing_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	uuid "github.com/satori/go.uuid"
	"strconv"
)

type SmartRoutingForecastTask struct {
	ForecastSrv  smart_routing_forecast.SmartRoutingForecastService
	ForecastRepo forecastrepo.IForecastRepo
}

func NewSmartRoutingForecastTask(
	ForecastSrv smart_routing_forecast.SmartRoutingForecastService,
	ForecastRepo forecastrepo.IForecastRepo,
) *SmartRoutingForecastTask {
	return &SmartRoutingForecastTask{
		ForecastSrv:  ForecastSrv,
		ForecastRepo: ForecastRepo,
	}
}

func (p *SmartRoutingForecastTask) Name() string {
	return constant.TaskNameSmartRoutingForecast
}

func (p *SmartRoutingForecastTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	// get pending task list
	task, err := p.ForecastRepo.GetFirstPendingTaskWithRoutingType(ctx, []int{rule.CBRoutingType, rule.IlhRoutingType})
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetFirstPendingTask fail|err=%+v", err)
		return err
	}

	if task == nil {
		logger.CtxLogInfof(ctx, "No pending forecasting task")
		return nil
	}

	requestID := fmt.Sprintf("%s|task_id=%d", uuid.NewV4().String(), task.Id) // nolint
	ctx = logger.NewLogContext(ctx, requestID)
	ctx = requestid.SetToCtx(ctx, requestID)
	ctx = forecast.NewForecastCtxWithTaskId(ctx, task.Id)
	logger.CtxLogInfof(ctx, "start forecast task: %d", task.Id)

	// start forecasting
	if err := p.ForecastSrv.StartForecast(ctx, task); err != nil {
		logger.CtxLogErrorf(ctx, "StartForecast fail|err=%+v", err)
		// update status to processing
		task.TaskStatus = persistent.TaskStatusFailed
		if _, err := p.ForecastRepo.UpdateTask(ctx, task); err != nil {
			logger.CtxLogErrorf(ctx, "UpdateTask fail|err=%+v", err)
		}
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleTask, "SmartRoutingForecast", monitoring.StatusError, err.Error())
		return err
	}

	task.TaskStatus = persistent.TaskStatusDone
	if _, err := p.ForecastRepo.UpdateTask(ctx, task); err != nil {
		logger.CtxLogErrorf(ctx, "UpdateTask fail|err=%+v", err)
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleTask, "SmartRoutingForecast", monitoring.StatusError, err.Error())
		return err
	}

	_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleTask, "SmartRoutingForecast", monitoring.StatusSuccess, strconv.Itoa(task.Id))

	return nil
}
