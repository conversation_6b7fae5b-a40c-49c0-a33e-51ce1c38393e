CREATE TABLE `volume_zone_group_tab` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `group_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL ,
  `zone_type` int(11) NOT NULL,
  `group_capacity` int(11) NOT NULL,
  `operator` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ctime` int(11) unsigned NOT NULL,
  `mtime` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_group_id_unique` (`group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `group_product_line_ref_tab` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mask_product_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `line_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `operator` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ctime` int(11) unsigned NOT NULL,
  `mtime` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_group_id` (`group_id`) USING BTREE,
  KEY `idx_line_id` (`line_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `group_zone_capacity_ref_tab` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `zone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `capacity` int(11) NOT NULL,
  `operator` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ctime` int(11) unsigned NOT NULL,
  `mtime` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_group_id_zone_name_unique` (`group_id`,`zone_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `volume_zone_location_tab` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `zone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `region` varchar(2) COLLATE utf8mb4_unicode_ci NOT NULL,
  `location_id` int(11) NOT NULL,
  `state` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `city` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `district` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `street` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `operator` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ctime` int(11) unsigned NOT NULL,
  `mtime` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_group_location` (`group_id`, `location_id`) USING BTREE,
  KEY `idx_zone` (`zone_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `volume_zone_postcode_tab` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `zone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `region` varchar(2) COLLATE utf8mb4_unicode_ci NOT NULL,
  `postcode` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `operator` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ctime` int(11) unsigned NOT NULL,
  `mtime` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_group_postcode` (`group_id`,`postcode`) USING BTREE,
  KEY `idx_zone` (`zone_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `volume_zone_cep_range_tab` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `zone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `region` varchar(2) COLLATE utf8mb4_unicode_ci NOT NULL,
  `cep_initial` int(11) NOT NULL,
  `cep_final` int(11) NOT NULL,
  `operator` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ctime` int(11) unsigned NOT NULL,
  `mtime` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_group_zone_cep` (`group_id`,`cep_initial`, `cep_final`) USING BTREE,
  KEY `idx_zone` (`zone_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `volume_task_record_tab` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `task_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `operation_type` tinyint(8) NOT NULL,
  `zone_group_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_name` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `fail_reason` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remote_path` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `task_status` tinyint(8) NOT NULL,
  `param` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `operator` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ctime` int(11) unsigned NOT NULL,
  `mtime` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_task_id_unique` (`task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `volume_routing_rule_tab` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `product_id` int(11) NOT NULL,
  `rule_type` tinyint(8) NOT NULL,
  `priority` int(11) NOT NULL,
  `effective_start_time` int(11) NOT NULL,
  `effective_immediately` tinyint(1) NOT NULL,
  `rule_status` tinyint(8) NOT NULL, 
  `operator` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ctime` int(11) unsigned NOT NULL,
  `mtime` int(11) unsigned NOT NULL,
  `data_version` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`) USING BTREE,
  KEY `idx_effective_start_time` (`effective_start_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `volume_routing_rule_detail_tab` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `rule_id` bigint(20) NOT NULL,
  `zone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `line_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `line_type` int(11) NOT NULL,
  `component_product_id` int(11) NOT NULL,
  `min_capacity` int(11) NOT NULL,
  `max_capacity` int(11) NOT NULL,
  `operator` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ctime` int(11) unsigned NOT NULL,
  `mtime` int(11) unsigned NOT NULL,
  `data_version` bigint(20) NOT NULL,
  `group_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_rule_id_line_id` (`rule_id`, `line_id`) USING BTREE,
  KEY `idx_zone_name` (`zone_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `smart_routing_task_lock_tab` (
                                               `id` bigint(20) NOT NULL AUTO_INCREMENT ,
                                               `biz_mode` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL ,
                                               `lock_key` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL ,
                                               `resource_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
                                               `lock_time` int(11) unsigned NOT NULL,
                                               `expire_time` int(11) unsigned NOT NULL,
                                               `ctime` int(11) unsigned NOT NULL,
                                               `mtime` int(11) unsigned NOT NULL,
                                               PRIMARY KEY (`id`),
                                               UNIQUE KEY `idx_biz_groupid_unique` (`biz_mode`,`lock_key`) USING BTREE,
                                               KEY `idx_expire_time` (`expire_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




